/**
 * Timezone utility functions for consistent time handling across the app
 */

/**
 * Converts a UTC date to Eastern Time (EST/EDT)
 * @param date - Date to convert (can be Date object or ISO string)
 * @returns Date object in Eastern Time
 */
export function toEasternTime(date: Date | string): Date {
  const utcDate = typeof date === 'string' ? new Date(date) : date
  
  // Create a new date in Eastern timezone
  const easternDate = new Date(utcDate.toLocaleString("en-US", {timeZone: "America/New_York"}))
  return easternDate
}

/**
 * Formats a date/time for display in Eastern Time
 * @param date - Date to format
 * @param options - Formatting options
 * @returns Formatted string with EST/EDT timezone
 */
export function formatEasternTime(
  date: Date | string, 
  options: {
    includeDate?: boolean
    includeTime?: boolean
    includeTimezone?: boolean
    format?: 'short' | 'long'
  } = {}
): string {
  const {
    includeDate = true,
    includeTime = true,
    includeTimezone = true,
    format = 'short'
  } = options

  const utcDate = typeof date === 'string' ? new Date(date) : date
  
  // Check if date is valid
  if (isNaN(utcDate.getTime())) {
    return 'Invalid Date'
  }

  const parts: string[] = []

  if (includeDate) {
    if (format === 'long') {
      parts.push(utcDate.toLocaleDateString("en-US", {
        timeZone: "America/New_York",
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      }))
    } else {
      parts.push(utcDate.toLocaleDateString("en-US", {
        timeZone: "America/New_York",
        month: 'numeric',
        day: 'numeric'
      }))
    }
  }

  if (includeTime) {
    const timeStr = utcDate.toLocaleTimeString("en-US", {
      timeZone: "America/New_York",
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
    parts.push(timeStr)
  }

  let result = parts.join(' ')

  if (includeTimezone) {
    // Determine if we're in EST or EDT
    const easternDate = new Date(utcDate.toLocaleString("en-US", {timeZone: "America/New_York"}))
    const utcOffset = easternDate.getTimezoneOffset()
    const isDST = utcOffset === 240 // EDT is UTC-4 (240 minutes), EST is UTC-5 (300 minutes)
    
    result += ` ${isDST ? 'EDT' : 'EST'}`
  }

  return result
}

/**
 * Gets the current time in Eastern timezone
 * @returns Date object representing current Eastern time
 */
export function getCurrentEasternTime(): Date {
  return toEasternTime(new Date())
}

/**
 * Checks if a date is today in Eastern timezone
 * @param date - Date to check
 * @returns boolean
 */
export function isToday(date: Date | string): boolean {
  const checkDate = toEasternTime(date)
  const today = getCurrentEasternTime()
  
  return checkDate.toDateString() === today.toDateString()
}

/**
 * Checks if a date is tomorrow in Eastern timezone
 * @param date - Date to check
 * @returns boolean
 */
export function isTomorrow(date: Date | string): boolean {
  const checkDate = toEasternTime(date)
  const tomorrow = getCurrentEasternTime()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  return checkDate.toDateString() === tomorrow.toDateString()
}

/**
 * Gets a relative time string (e.g., "in 2 hours", "tomorrow at 3:00 PM")
 * @param date - Target date
 * @returns Relative time string in Eastern timezone
 */
export function getRelativeEasternTime(date: Date | string): string {
  const targetDate = toEasternTime(date)
  const now = getCurrentEasternTime()
  
  const diffMs = targetDate.getTime() - now.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))

  if (diffMs < 0) {
    // Past date
    const absDiffHours = Math.abs(diffHours)
    if (absDiffHours < 1) {
      return 'Just finished'
    } else if (absDiffHours < 24) {
      return `${absDiffHours}h ago`
    } else {
      return formatEasternTime(date, { includeDate: true, includeTime: true, format: 'short' })
    }
  }

  if (isToday(date)) {
    if (diffHours < 1) {
      return `in ${diffMinutes}m`
    } else {
      return `in ${diffHours}h ${diffMinutes}m`
    }
  } else if (isTomorrow(date)) {
    return `tomorrow at ${formatEasternTime(date, { includeDate: false, includeTime: true, includeTimezone: false })}`
  } else {
    return formatEasternTime(date, { includeDate: true, includeTime: true, format: 'short' })
  }
}

/**
 * Formats game time specifically for sports betting display
 * @param gameTime - Game start time
 * @returns Formatted string optimized for sports display
 */
export function formatGameTime(gameTime: Date | string): string {
  const date = typeof gameTime === 'string' ? new Date(gameTime) : gameTime
  
  if (isNaN(date.getTime())) {
    return 'TBD'
  }

  if (isToday(date)) {
    return `Today ${formatEasternTime(date, { includeDate: false, includeTime: true, includeTimezone: false })}`
  } else if (isTomorrow(date)) {
    return `Tomorrow ${formatEasternTime(date, { includeDate: false, includeTime: true, includeTimezone: false })}`
  } else {
    return formatEasternTime(date, { 
      includeDate: true, 
      includeTime: true, 
      includeTimezone: false,
      format: 'short'
    })
  }
}
