// Smart Caching + Incremental Updates
// Minimize AI calls by intelligent caching and only updating when necessary

import { createSupabaseServerClient } from '@/lib/supabase/server'
import { analyzeWithDeepSeek } from './deepseek-analyzer'
import { pipelineAnalysis } from './pipeline-analyzer'

interface CacheEntry {
  prop_id: string
  analysis_data: any
  line_value: number
  created_at: string
  player_name: string
  prop_type: string
  game_time: string
  cache_key: string
}

interface UpdateTrigger {
  line_changed: boolean
  injury_update: boolean
  significant_news: boolean
  stale_analysis: boolean
  never_analyzed: boolean
}

// Generate cache key for analysis
function generateCacheKey(propData: any): string {
  const keyComponents = [
    propData.player.name.toLowerCase().replace(/\s+/g, ''),
    propData.prop.type.toLowerCase(),
    propData.prop.line.toString(),
    propData.opponent.name.toLowerCase().replace(/\s+/g, ''),
    new Date(propData.prop.gameTime).toDateString()
  ]
  return keyComponents.join('|')
}

// Check if analysis needs updating
async function shouldUpdateAnalysis(propData: any): Promise<UpdateTrigger> {
  const supabase = createSupabaseServerClient()
  const cacheKey = generateCacheKey(propData)
  
  // Check for existing analysis
  const { data: existingAnalysis } = await supabase
    .from('ai_analyses')
    .select('*')
    .eq('prop_id', propData.id)
    .order('created_at', { ascending: false })
    .limit(1)
    .single()

  if (!existingAnalysis) {
    return {
      line_changed: false,
      injury_update: false,
      significant_news: false,
      stale_analysis: false,
      never_analyzed: true
    }
  }

  const triggers: UpdateTrigger = {
    line_changed: false,
    injury_update: false,
    significant_news: false,
    stale_analysis: false,
    never_analyzed: false
  }

  // Check for line changes
  const { data: lineMovements } = await supabase
    .from('line_movements')
    .select('*')
    .eq('prop_id', propData.id)
    .gte('detected_at', existingAnalysis.created_at)
    .order('detected_at', { ascending: false })

  if (lineMovements && lineMovements.length > 0) {
    const significantMove = lineMovements.some(move => 
      Math.abs(move.new_line - move.old_line) >= 0.5 // 0.5+ line movement
    )
    triggers.line_changed = significantMove
  }

  // Check for injury updates (simplified - would integrate with injury API)
  const analysisAge = Date.now() - new Date(existingAnalysis.created_at).getTime()
  const hoursOld = analysisAge / (1000 * 60 * 60)
  
  // Consider stale if over 8 hours old for same-day games
  const gameDate = new Date(propData.prop.gameTime)
  const isToday = gameDate.toDateString() === new Date().toDateString()
  triggers.stale_analysis = isToday && hoursOld > 8

  // Check cache key mismatch (indicates data change)
  const existingCacheKey = existingAnalysis.cache_key || ''
  if (existingCacheKey !== cacheKey) {
    triggers.line_changed = true
  }

  return triggers
}

// Smart analysis with caching
export async function smartCachedAnalysis(propData: any, forceRefresh = false) {
  const supabase = createSupabaseServerClient()
  
  if (!forceRefresh) {
    const updateTriggers = await shouldUpdateAnalysis(propData)
    
    // Return cached analysis if no updates needed
    if (!Object.values(updateTriggers).some(trigger => trigger)) {
      const { data: cachedAnalysis } = await supabase
        .from('ai_analyses')
        .select('*')
        .eq('prop_id', propData.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (cachedAnalysis) {
        console.log(`Using cached analysis: ${propData.player.name} ${propData.prop.type}`)
        return {
          ...cachedAnalysis,
          cache_hit: true,
          cost_estimate: 0
        }
      }
    }

    console.log(`Update triggers for ${propData.player.name}:`, updateTriggers)
  }

  // Determine analysis method based on priority
  const priority = calculateAnalysisPriority(propData)
  let analysisResult

  if (priority > 70) {
    // High priority: Use pipeline approach (Claude research + cheaper breakdown)
    analysisResult = await pipelineAnalysis(propData, { useGroq: true })
  } else if (priority > 40) {
    // Medium priority: Use DeepSeek with enhanced prompting
    analysisResult = await analyzeWithDeepSeek(propData)
  } else {
    // Low priority: Quick DeepSeek analysis
    analysisResult = await analyzeWithDeepSeek(propData)
  }

  // Save to cache with metadata
  const cacheKey = generateCacheKey(propData)
  const { error: saveError } = await supabase
    .from('ai_analyses')
    .upsert({
      prop_id: propData.id,
      analysis_text: analysisResult.analysis_text,
      confidence_rating: analysisResult.confidence_rating,
      recommendation: analysisResult.recommendation,
      reasoning_points: analysisResult.reasoning_points,
      edge_summary: analysisResult.edge_summary,
      cache_key: cacheKey,
      analysis_method: analysisResult.analysis_method || 'smart_cached',
      cost_estimate: analysisResult.cost_estimate || 0.003
    })

  if (saveError) {
    console.error('Failed to save cached analysis:', saveError)
  }

  return {
    ...analysisResult,
    cache_hit: false,
    cache_key: cacheKey
  }
}

// Calculate analysis priority for method selection
function calculateAnalysisPriority(propData: any): number {
  let priority = 0

  // Player tier (star players get priority)
  const seasonStats = propData.player.seasonStats || {}
  const avgStat = Object.values(seasonStats).reduce((sum: number, val: any) => sum + (Number(val) || 0), 0) / Object.keys(seasonStats).length
  if (avgStat > 20) priority += 30 // Star player
  else if (avgStat > 15) priority += 20 // Above average
  else if (avgStat > 10) priority += 10 // Average

  // Prop type importance
  const highValueProps = ['points', 'strikeouts', 'hits', 'rebounds', 'assists']
  if (highValueProps.some(prop => propData.prop.type.toLowerCase().includes(prop))) {
    priority += 25
  }

  // Game timing (prime time = higher priority)
  const gameHour = new Date(propData.prop.gameTime).getHours()
  if (gameHour >= 19 && gameHour <= 22) priority += 15

  // Line movement significance
  if (propData.sharpLines && propData.sharpLines.length > 0) {
    const maxEdge = Math.max(...propData.sharpLines.map((line: any) => Math.abs(line.edge_percentage || 0)))
    if (maxEdge > 5) priority += 30
    else if (maxEdge > 3) priority += 15
  }

  // Injury/news impact
  if (propData.context.injuries && propData.context.injuries.length > 0) {
    priority += 20
  }

  return Math.min(priority, 100) // Cap at 100
}

// Batch processing with smart caching
export async function batchSmartCachedAnalysis(propDataArray: any[], options = { forceRefresh: false, maxConcurrent: 10 }) {
  const results = []
  let cacheHits = 0
  let newAnalyses = 0
  let totalCost = 0

  // Process in batches
  for (let i = 0; i < propDataArray.length; i += options.maxConcurrent) {
    const batch = propDataArray.slice(i, i + options.maxConcurrent)
    
    const batchPromises = batch.map(async (propData, index) => {
      try {
        // Stagger requests
        await new Promise(resolve => setTimeout(resolve, index * 100))
        
        const result = await smartCachedAnalysis(propData, options.forceRefresh)
        
        if (result.cache_hit) {
          cacheHits++
        } else {
          newAnalyses++
          totalCost += result.cost_estimate || 0
        }
        
        return result
        
      } catch (error) {
        console.error(`Smart cached analysis failed for ${propData.player.name}:`, error)
        return {
          analysis_text: 'Analysis failed',
          confidence_rating: 1,
          recommendation: 'AVOID',
          reasoning_points: ['Analysis error'],
          cache_hit: false,
          cost_estimate: 0
        }
      }
    })
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
    
    // Rate limiting between batches
    if (i + options.maxConcurrent < propDataArray.length) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  console.log(`Smart cached batch complete:`)
  console.log(`- Cache hits: ${cacheHits}`)
  console.log(`- New analyses: ${newAnalyses}`)
  console.log(`- Total cost: $${totalCost.toFixed(3)}`)
  console.log(`- Cache efficiency: ${((cacheHits / results.length) * 100).toFixed(1)}%`)

  return {
    results,
    stats: {
      cache_hits: cacheHits,
      new_analyses: newAnalyses,
      total_cost: totalCost,
      cache_efficiency: (cacheHits / results.length) * 100
    }
  }
}

// Invalidate cache for specific triggers
export async function invalidateAnalysisCache(criteria: {
  player_name?: string
  prop_type?: string
  game_date?: string
  older_than_hours?: number
}) {
  const supabase = createSupabaseServerClient()
  
  let query = supabase.from('ai_analyses').delete()
  
  if (criteria.player_name) {
    // Would need to join with props table for player name
    console.log('Player-specific cache invalidation not implemented')
    return
  }
  
  if (criteria.older_than_hours) {
    const cutoffTime = new Date(Date.now() - criteria.older_than_hours * 60 * 60 * 1000)
    query = query.lt('created_at', cutoffTime.toISOString())
  }
  
  const { error } = await query
  
  if (error) {
    console.error('Cache invalidation error:', error)
  } else {
    console.log('Cache invalidated successfully')
  }
}
