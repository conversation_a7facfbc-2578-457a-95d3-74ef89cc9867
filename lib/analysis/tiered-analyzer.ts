// Tiered Analysis Pipeline - Optimal Cost/Quality Balance
// Stage 1: DeepSeek for initial screening and basic analysis
// Stage 2: <PERSON> for high-value props and complex breakdowns

import OpenAI from 'openai'
import Anthropic from '@anthropic-ai/sdk'

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY
})

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!
})

interface PropAnalysisData {
  player: {
    name: string
    position: string
    team: string
    seasonStats: Record<string, number>
    last5Games: number[]
    vsOpponentHistory: number[]
  }
  opponent: {
    name: string
    defensiveRanking: number
    allowedPerGame: number
  }
  prop: {
    type: string
    line: number
    gameTime: string
  }
  context: {
    weather?: string
    injuries?: string[]
    gameScript?: string
  }
  sharpLines?: any[]
  gameContext?: any
}

interface TieredAnalysisResult {
  analysis_text: string
  confidence_rating: number
  recommendation: 'OVER' | 'UNDER' | 'AVOID'
  reasoning_points: string[]
  edge_summary?: string
  tier_used: 'deepseek' | 'claude' | 'hybrid'
  cost_estimate: number
  priority_score: number
}

// Calculate prop priority for Claude upgrade
function calculatePropPriority(data: PropAnalysisData): number {
  let score = 0
  
  // High-value prop types get priority
  const highValueProps = ['points', 'rebounds', 'assists', 'strikeouts', 'hits', 'rbis']
  if (highValueProps.some(prop => data.prop.type.toLowerCase().includes(prop))) {
    score += 30
  }
  
  // Star players get priority (basic heuristic)
  const seasonStats = data.player.seasonStats
  if (Object.values(seasonStats).some(stat => stat > 20)) { // High performer
    score += 25
  }
  
  // Sharp line discrepancy indicates value
  if (data.sharpLines && data.sharpLines.length > 0) {
    const maxEdge = Math.max(...data.sharpLines.map(line => Math.abs(line.edge_percentage)))
    if (maxEdge > 5) score += 40 // Significant edge
    else if (maxEdge > 3) score += 20 // Moderate edge
  }
  
  // Prime time games get priority
  const gameHour = new Date(data.prop.gameTime).getHours()
  if (gameHour >= 19 && gameHour <= 22) { // 7-10 PM
    score += 15
  }
  
  // Injury concerns increase complexity
  if (data.context.injuries && data.context.injuries.length > 0) {
    score += 20
  }
  
  return score
}

// Stage 1: DeepSeek screening and basic analysis
async function deepSeekScreening(data: PropAnalysisData): Promise<TieredAnalysisResult> {
  const prompt = `Analyze this sports prop bet with available data:

PLAYER: ${data.player.name} (${data.player.position}, ${data.player.team})
PROP: ${data.prop.type} ${data.prop.line}
OPPONENT: ${data.opponent.name}

RECENT PERFORMANCE: ${data.player.last5Games.join(', ')}
SEASON AVERAGE: ${Object.entries(data.player.seasonStats).map(([k,v]) => `${k}: ${v}`).join(', ')}
OPPONENT DEFENSE: Rank ${data.opponent.defensiveRanking}, allows ${data.opponent.allowedPerGame}/game

CONTEXT: ${data.context.gameScript || 'Standard game script'}
${data.context.injuries ? `INJURIES: ${data.context.injuries.join(', ')}` : ''}

Provide a focused analysis in JSON format:
{
  "analysis": "2-3 sentence analysis focusing on key factors",
  "confidence": 1-5,
  "recommendation": "OVER" | "UNDER" | "AVOID",
  "keyFactors": ["factor1", "factor2", "factor3"],
  "needsUpgrade": true/false (true if complex factors require deeper research),
  "upgradeReason": "why this needs Claude analysis (if needsUpgrade is true)"
}`

  try {
    const response = await deepseek.chat.completions.create({
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      max_tokens: 600,
      response_format: { type: 'json_object' }
    })

    const result = JSON.parse(response.choices[0]?.message?.content || '{}')
    
    return {
      analysis_text: result.analysis || 'Analysis failed',
      confidence_rating: Math.min(Math.max(result.confidence || 3, 1), 5),
      recommendation: result.recommendation || 'AVOID',
      reasoning_points: result.keyFactors || [],
      tier_used: 'deepseek',
      cost_estimate: 0.003,
      priority_score: calculatePropPriority(data),
      needs_upgrade: result.needsUpgrade || false,
      upgrade_reason: result.upgradeReason
    } as TieredAnalysisResult & { needs_upgrade: boolean; upgrade_reason?: string }
    
  } catch (error) {
    console.error('DeepSeek screening error:', error)
    throw error
  }
}

// Stage 2: Claude deep analysis for high-priority props
async function claudeDeepAnalysis(data: PropAnalysisData, deepSeekResult: any): Promise<TieredAnalysisResult> {
  const prompt = `You are an expert sports analyst. Provide comprehensive analysis for this high-priority prop bet.

INITIAL SCREENING (DeepSeek): ${deepSeekResult.analysis_text}
UPGRADE REASON: ${deepSeekResult.upgrade_reason}

DETAILED DATA:
Player: ${data.player.name} (${data.player.position}, ${data.player.team})
Prop: ${data.prop.type} ${data.prop.line}
Opponent: ${data.opponent.name} (Def Rank: ${data.opponent.defensiveRanking})

Performance Data:
- Season Stats: ${Object.entries(data.player.seasonStats).map(([k,v]) => `${k}: ${v}`).join(', ')}
- Last 5 Games: ${data.player.last5Games.join(', ')}
- vs This Opponent: ${data.player.vsOpponentHistory.join(', ') || 'No history'}

${data.sharpLines ? `Sharp Line Comparison:
${data.sharpLines.map(line => `- ${line.bookmaker}: ${line.line} (${line.edge_percentage > 0 ? '+' : ''}${line.edge_percentage.toFixed(1)}% edge)`).join('\n')}` : ''}

Game Context:
- Script: ${data.context.gameScript || 'Standard'}
- Injuries: ${data.context.injuries?.join(', ') || 'None reported'}
- Weather: ${data.context.weather || 'Indoor/No impact'}

Provide comprehensive analysis in JSON format:
{
  "analysis_text": "Detailed 4-5 paragraph analysis with specific data points, matchup analysis, and line value assessment",
  "confidence_rating": 1-5,
  "recommendation": "OVER" | "UNDER" | "AVOID",
  "reasoning_points": [
    "Primary statistical factor with specific numbers",
    "Matchup advantage/disadvantage with context",
    "Line value assessment vs sharp books",
    "Risk factors and variance considerations"
  ],
  "edge_summary": "Summary of line value and betting edge",
  "correlations": ["Related prop suggestion 1", "Related prop suggestion 2"]
}`

  try {
    const response = await anthropic.messages.create({
      model: 'claude-sonnet-4-20250514',
      max_tokens: 2000,
      temperature: 0.2,
      messages: [{ role: 'user', content: prompt }]
    })

    const analysisText = response.content[0]?.type === 'text' ? response.content[0].text : ''
    const result = JSON.parse(analysisText)
    
    return {
      analysis_text: result.analysis_text,
      confidence_rating: result.confidence_rating,
      recommendation: result.recommendation,
      reasoning_points: result.reasoning_points || [],
      edge_summary: result.edge_summary,
      tier_used: 'claude',
      cost_estimate: 0.15,
      priority_score: calculatePropPriority(data)
    }
    
  } catch (error) {
    console.error('Claude deep analysis error:', error)
    // Fallback to enhanced DeepSeek result
    return {
      ...deepSeekResult,
      tier_used: 'claude',
      cost_estimate: 0.15,
      analysis_text: `${deepSeekResult.analysis_text}\n\n[Claude analysis failed - using enhanced DeepSeek result]`
    }
  }
}

// Main tiered analysis function
export async function tieredAnalysis(data: PropAnalysisData): Promise<TieredAnalysisResult> {
  // Stage 1: Always start with DeepSeek screening
  const deepSeekResult = await deepSeekScreening(data) as TieredAnalysisResult & { needs_upgrade: boolean; upgrade_reason?: string }
  
  // Stage 2: Upgrade to Claude based on priority and complexity
  const priorityScore = calculatePropPriority(data)
  const shouldUpgrade = deepSeekResult.needs_upgrade || priorityScore > 60
  
  if (shouldUpgrade) {
    console.log(`Upgrading to Claude: ${data.player.name} ${data.prop.type} (Priority: ${priorityScore})`)
    return await claudeDeepAnalysis(data, deepSeekResult)
  }
  
  // Stage 3: Enhanced DeepSeek for medium priority
  if (priorityScore > 30) {
    return {
      ...deepSeekResult,
      analysis_text: `${deepSeekResult.analysis_text}\n\nPRIORITY FACTORS: ${deepSeekResult.upgrade_reason || 'Standard analysis sufficient'}`,
      tier_used: 'deepseek'
    }
  }
  
  return deepSeekResult
}

// Batch processing with intelligent prioritization
export async function batchTieredAnalysis(dataArray: PropAnalysisData[]): Promise<TieredAnalysisResult[]> {
  const results: TieredAnalysisResult[] = []
  
  // Sort by priority for optimal Claude usage
  const sortedData = dataArray
    .map(data => ({ data, priority: calculatePropPriority(data) }))
    .sort((a, b) => b.priority - a.priority)
  
  let claudeUsageCount = 0
  const maxClaudeUsage = Math.min(50, Math.floor(dataArray.length * 0.25)) // Max 25% or 50 props
  
  // Process in batches
  for (let i = 0; i < sortedData.length; i += 10) {
    const batch = sortedData.slice(i, i + 10)
    
    const batchPromises = batch.map(async ({ data }) => {
      try {
        // Force DeepSeek if Claude quota exceeded
        if (claudeUsageCount >= maxClaudeUsage) {
          const deepSeekResult = await deepSeekScreening(data)
          return { ...deepSeekResult, tier_used: 'deepseek' as const }
        }
        
        const result = await tieredAnalysis(data)
        if (result.tier_used === 'claude') {
          claudeUsageCount++
        }
        return result
        
      } catch (error) {
        console.error(`Analysis failed for ${data.player.name}:`, error)
        return {
          analysis_text: 'Analysis failed - manual review needed',
          confidence_rating: 1,
          recommendation: 'AVOID' as const,
          reasoning_points: ['Analysis error'],
          tier_used: 'deepseek' as const,
          cost_estimate: 0,
          priority_score: 0
        }
      }
    })
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
    
    // Rate limiting
    if (i + 10 < sortedData.length) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }
  
  console.log(`Batch analysis complete: ${claudeUsageCount} Claude, ${results.length - claudeUsageCount} DeepSeek`)
  return results
}
