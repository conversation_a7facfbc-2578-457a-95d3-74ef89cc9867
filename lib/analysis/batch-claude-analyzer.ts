// Batch Claude Analysis + DeepSeek Breakdown Pipeline
// Stage 1: Claude batch research + decisions (efficient context sharing)
// Stage 2: DeepSeek breakdown generation (cost-effective formatting)

import Anthropic from '@anthropic-ai/sdk'
import OpenAI from 'openai'

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!
})

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY
})

interface BatchPropData {
  id: string
  player_name: string
  team: string
  opponent?: string
  prop_type: string
  line_value: number
  game_time: string
  priority: number
}

interface ClaudeResearchResult {
  prop_id: string
  research_summary: string
  decision: 'OVER' | 'UNDER' | 'AVOID'
  confidence_rating: number
  key_factors: string[]
  edge_assessment: string
  supporting_data: Record<string, any>
}

interface FinalAnalysisResult {
  prop_id: string
  analysis_text: string
  confidence_rating: number
  recommendation: 'OVER' | 'UNDER' | 'AVOID'
  reasoning_points: string[]
  edge_summary: string
  breakdown_text: string
  cost_breakdown: {
    claude_cost: number
    deepseek_cost: number
    total_cost: number
  }
}

// Main production-ready batch analysis pipeline
export async function batchClaudeAnalysis(props: BatchPropData[]): Promise<FinalAnalysisResult[]> {
  console.log(`🚀 Starting batch analysis for ${props.length} props`)

  // Group props by game for optimal context sharing
  const gameGroups = groupPropsByGame(props)
  const results: FinalAnalysisResult[] = []

  let totalClaudeCost = 0
  let totalDeepSeekCost = 0

  for (const [gameKey, gameProps] of gameGroups.entries()) {
    console.log(`📊 Processing game: ${gameKey} (${gameProps.length} props)`)

    try {
      // Stage 1: Claude batch research for this game
      const claudeResults = await claudeBatchResearch(gameProps)

      // Stage 2: DeepSeek breakdown generation for each prop
      const finalResults = await Promise.all(
        claudeResults.map(async (claudeResult, index) => {
          const propData = gameProps[index]

          try {
            const breakdown = await generateDeepSeekBreakdown(claudeResult, propData)

            const claudeCost = estimateClaudeCost(gameProps.length)
            const deepSeekCost = 0.003

            totalClaudeCost += claudeCost / gameProps.length // Split cost across props
            totalDeepSeekCost += deepSeekCost

            return {
              prop_id: claudeResult.prop_id,
              analysis_text: breakdown.analysis_text,
              confidence_rating: claudeResult.confidence_rating,
              recommendation: claudeResult.decision,
              reasoning_points: breakdown.reasoning_points,
              edge_summary: breakdown.edge_summary,
              breakdown_text: breakdown.breakdown_text,
              cost_breakdown: {
                claude_cost: claudeCost / gameProps.length,
                deepseek_cost: deepSeekCost,
                total_cost: (claudeCost / gameProps.length) + deepSeekCost
              }
            }
          } catch (breakdownError) {
            console.error(`DeepSeek breakdown failed for ${propData.player_name}:`, breakdownError)

            // Fallback to Claude-only result
            return {
              prop_id: claudeResult.prop_id,
              analysis_text: claudeResult.research_summary,
              confidence_rating: claudeResult.confidence_rating,
              recommendation: claudeResult.decision,
              reasoning_points: claudeResult.key_factors,
              edge_summary: claudeResult.edge_assessment,
              breakdown_text: claudeResult.research_summary,
              cost_breakdown: {
                claude_cost: estimateClaudeCost(gameProps.length) / gameProps.length,
                deepseek_cost: 0,
                total_cost: estimateClaudeCost(gameProps.length) / gameProps.length
              }
            }
          }
        })
      )

      results.push(...finalResults)

      // Rate limiting between games
      if (gameGroups.size > 1) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }

    } catch (gameError) {
      console.error(`Game analysis failed for ${gameKey}:`, gameError)

      // Add fallback results for entire game
      gameProps.forEach(prop => {
        results.push({
          prop_id: prop.id,
          analysis_text: 'Game analysis failed - manual review needed',
          confidence_rating: 1,
          recommendation: 'AVOID',
          reasoning_points: ['Analysis error'],
          edge_summary: 'Unable to analyze',
          breakdown_text: 'Analysis failed',
          cost_breakdown: {
            claude_cost: 0,
            deepseek_cost: 0,
            total_cost: 0
          }
        })
      })
    }
  }

  console.log(`✅ Batch analysis complete:`)
  console.log(`   Claude cost: $${totalClaudeCost.toFixed(3)}`)
  console.log(`   DeepSeek cost: $${totalDeepSeekCost.toFixed(3)}`)
  console.log(`   Total cost: $${(totalClaudeCost + totalDeepSeekCost).toFixed(3)}`)
  console.log(`   Average per prop: $${((totalClaudeCost + totalDeepSeekCost) / props.length).toFixed(4)}`)

  return results
}

// Group props by game for optimal context sharing
function groupPropsByGame(props: BatchPropData[]): Map<string, BatchPropData[]> {
  const gameGroups = new Map<string, BatchPropData[]>()

  props.forEach(prop => {
    // Create game key from teams and date
    const gameDate = new Date(prop.game_time).toDateString()
    const teams = [prop.team, prop.opponent].filter(Boolean).sort().join(' vs ')
    const gameKey = `${teams} - ${gameDate}`

    if (!gameGroups.has(gameKey)) {
      gameGroups.set(gameKey, [])
    }
    gameGroups.get(gameKey)!.push(prop)
  })

  return gameGroups
}

// Stage 1: Claude batch research for a game
async function claudeBatchResearch(gameProps: BatchPropData[]): Promise<ClaudeResearchResult[]> {
  const prompt = buildClaudeResearchPrompt(gameProps)

  const response = await anthropic.messages.create({
    model: 'claude-sonnet-4-20250514',
    max_tokens: 3000 + (gameProps.length * 500), // Scale with prop count
    temperature: 0.2,
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ]
  })

  const analysisText = response.content[0]?.type === 'text' ? response.content[0].text : ''

  try {
    const batchResult = JSON.parse(analysisText)

    // Validate we got results for all props
    if (!batchResult.analyses || batchResult.analyses.length !== gameProps.length) {
      throw new Error(`Expected ${gameProps.length} analyses, got ${batchResult.analyses?.length || 0}`)
    }

    return batchResult.analyses.map((analysis: any, index: number) => ({
      prop_id: gameProps[index].id,
      research_summary: analysis.research_summary || 'Research missing',
      decision: analysis.decision || 'AVOID',
      confidence_rating: Math.min(Math.max(analysis.confidence_rating || 3, 1), 5),
      key_factors: analysis.key_factors || [],
      edge_assessment: analysis.edge_assessment || 'No edge assessment',
      supporting_data: analysis.supporting_data || {}
    }))

  } catch (parseError) {
    console.error('Failed to parse Claude research:', parseError)
    throw new Error('Claude research parsing failed')
  }
}

// Stage 2: DeepSeek breakdown generation
async function generateDeepSeekBreakdown(
  claudeResult: ClaudeResearchResult,
  propData: BatchPropData
): Promise<{
  analysis_text: string
  reasoning_points: string[]
  edge_summary: string
  breakdown_text: string
}> {
  const prompt = `Create a detailed breakdown for this sports betting analysis.

CLAUDE RESEARCH: ${claudeResult.research_summary}
DECISION: ${claudeResult.decision} (Confidence: ${claudeResult.confidence_rating}/5)
KEY FACTORS: ${claudeResult.key_factors.join(', ')}
EDGE ASSESSMENT: ${claudeResult.edge_assessment}

PROP DETAILS:
- Player: ${propData.player_name}
- Bet: ${propData.prop_type} ${propData.line_value}
- Team: ${propData.team}${propData.opponent ? ` vs ${propData.opponent}` : ''}
- Game: ${new Date(propData.game_time).toLocaleDateString()}

Create a professional breakdown in JSON format:
{
  "analysis_text": "4-5 paragraph detailed analysis incorporating all research findings",
  "reasoning_points": [
    "Point 1 with specific data",
    "Point 2 with matchup details",
    "Point 3 with line value assessment",
    "Point 4 with risk considerations"
  ],
  "edge_summary": "Concise summary of betting edge and value",
  "breakdown_text": "Clean, readable summary for popup display (2-3 paragraphs)"
}`

  try {
    const response = await deepseek.chat.completions.create({
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      max_tokens: 1000,
      response_format: { type: 'json_object' }
    })

    const content = response.choices[0]?.message?.content || '{}'
    const result = JSON.parse(content)

    return {
      analysis_text: result.analysis_text || claudeResult.research_summary,
      reasoning_points: result.reasoning_points || claudeResult.key_factors,
      edge_summary: result.edge_summary || claudeResult.edge_assessment,
      breakdown_text: result.breakdown_text || claudeResult.research_summary
    }

  } catch (error) {
    console.error('DeepSeek breakdown error:', error)
    // Fallback to Claude data
    return {
      analysis_text: claudeResult.research_summary,
      reasoning_points: claudeResult.key_factors,
      edge_summary: claudeResult.edge_assessment,
      breakdown_text: claudeResult.research_summary
    }
  }
}

// Build Claude research prompt for a game
function buildClaudeResearchPrompt(gameProps: BatchPropData[]): string {
  // Extract game context
  const gameDate = new Date(gameProps[0].game_time).toLocaleDateString()
  const teams = [...new Set([gameProps[0].team, gameProps[0].opponent].filter(Boolean))].join(' vs ')

  const propsText = gameProps.map((prop, index) => `
PROP ${index + 1}:
- ID: ${prop.id}
- Player: ${prop.player_name}
- Bet: ${prop.prop_type} ${prop.line_value}
- Priority: ${prop.priority}/100`).join('\n')

  return `You are an elite sports analyst researching this game for betting decisions.

GAME CONTEXT:
- Matchup: ${teams}
- Date: ${gameDate}
- Props to analyze: ${gameProps.length}

PROPS TO RESEARCH:${propsText}

For this game, research and decide on each prop considering:

1. **GAME SCRIPT & CONTEXT**
   - Projected game flow (competitive vs blowout potential)
   - Total/spread implications for prop volume
   - Pace of play and possession projections
   - Weather impact (if outdoor)

2. **TEAM DYNAMICS**
   - Recent form and momentum
   - Key injuries affecting game plan
   - Coaching tendencies and matchup history
   - Motivational factors (playoff implications, etc.)

3. **INDIVIDUAL PLAYER ANALYSIS**
   - Recent performance trends (last 5 games)
   - Season averages and consistency
   - Matchup advantages/disadvantages
   - Usage rate and opportunity factors

4. **LINE VALUE ASSESSMENT**
   - Compare to season averages and recent form
   - Identify potential market inefficiencies
   - Consider variance and floor/ceiling outcomes

Respond with JSON in this exact format:
{
  "game_context": {
    "projected_script": "competitive|blowout_risk|defensive_battle",
    "pace_projection": "fast|average|slow",
    "key_factors": ["factor1", "factor2", "factor3"]
  },
  "analyses": [
    {
      "research_summary": "Comprehensive research findings for Prop 1 (2-3 paragraphs)",
      "decision": "OVER" | "UNDER" | "AVOID",
      "confidence_rating": 1-5,
      "key_factors": ["Primary factor", "Secondary factor", "Risk factor"],
      "edge_assessment": "Line value analysis vs expected performance",
      "supporting_data": {
        "recent_average": 0.0,
        "season_average": 0.0,
        "matchup_factor": "positive|neutral|negative"
      }
    }
    // ... continue for all ${gameProps.length} props in order
  ]
}

IMPORTANT: Provide exactly ${gameProps.length} analyses in the same order as the props listed above.`
}

// Estimate Claude cost based on batch size
function estimateClaudeCost(propsInBatch: number): number {
  // Base prompt: ~1000 tokens
  // Per prop: ~400 tokens input + 600 tokens output
  // Shared game context saves ~200 tokens per prop after the first

  const baseTokens = 1000
  const firstPropTokens = 1000 // Full context
  const additionalPropTokens = 800 // Reduced due to shared context

  const totalTokens = baseTokens + firstPropTokens + ((propsInBatch - 1) * additionalPropTokens)
  return (totalTokens / 1_000_000) * 15 // $15 per million tokens
}

// Calculate token savings from batching
export function calculateBatchSavings(totalProps: number, batchSize: number = 5): {
  individual_cost: number
  batch_cost: number
  savings: number
  savings_percentage: number
} {
  const tokensPerIndividualAnalysis = 3000 // Input + output
  const tokensPerBatchOverhead = 1000 // Base prompt
  const tokensPerPropInBatch = 2000 // Reduced per-prop tokens due to shared context
  
  // Individual analysis cost
  const individualTotalTokens = totalProps * tokensPerIndividualAnalysis
  const individualCost = (individualTotalTokens / 1_000_000) * 15
  
  // Batch analysis cost
  const numBatches = Math.ceil(totalProps / batchSize)
  const batchTotalTokens = numBatches * (tokensPerBatchOverhead + (batchSize * tokensPerPropInBatch))
  const batchCost = (batchTotalTokens / 1_000_000) * 15
  
  return {
    individual_cost: individualCost,
    batch_cost: batchCost,
    savings: individualCost - batchCost,
    savings_percentage: ((individualCost - batchCost) / individualCost) * 100
  }
}

// Example usage and cost comparison
export function printCostComparison(totalProps: number) {
  const savings = calculateBatchSavings(totalProps)
  
  console.log(`\n📊 Cost Comparison for ${totalProps} props:`)
  console.log(`Individual Analysis: $${savings.individual_cost.toFixed(2)}/day`)
  console.log(`Batch Analysis: $${savings.batch_cost.toFixed(2)}/day`)
  console.log(`Savings: $${savings.savings.toFixed(2)}/day (${savings.savings_percentage.toFixed(1)}%)`)
  console.log(`Monthly Savings: $${(savings.savings * 30).toFixed(2)}`)
}
