// Research-Decision-Breakdown Pipeline
// Stage 1: <PERSON> for research and decision (expensive but accurate)
// Stage 2: Cheaper AI for breakdown formatting (cost-efficient)

import OpenAI from 'openai'
import Anthropic from '@anthropic-ai/sdk'

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!
})

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY
})

// Alternative: Use Groq for fast breakdown generation
const groq = new OpenAI({
  baseURL: 'https://api.groq.com/openai/v1',
  apiKey: process.env.GROQ_API_KEY
})

interface ResearchDecisionResult {
  research_summary: string
  decision: 'OVER' | 'UNDER' | 'AVOID'
  confidence: number
  key_factors: string[]
  edge_assessment: string
  risk_factors: string[]
  supporting_data: Record<string, any>
}

interface BreakdownResult {
  analysis_text: string
  reasoning_points: string[]
  edge_summary: string
  correlations: string[]
  formatted_breakdown: string
}

// Stage 1: <PERSON> for research and decision-making
async function claudeResearchAndDecision(data: any): Promise<ResearchDecisionResult> {
  const prompt = `You are a professional sports analyst. Research this prop bet and make a decision.

PROP: ${data.player.name} ${data.prop.type} ${data.prop.line}
TEAM: ${data.player.team} vs ${data.opponent.name}
GAME TIME: ${data.prop.gameTime}

AVAILABLE DATA:
- Season Stats: ${Object.entries(data.player.seasonStats).map(([k,v]) => `${k}: ${v}`).join(', ')}
- Last 5 Games: ${data.player.last5Games.join(', ')}
- Opponent Defense: Rank ${data.opponent.defensiveRanking}, allows ${data.opponent.allowedPerGame}/game
- Game Context: ${data.context.gameScript || 'Standard'}
- Injuries: ${data.context.injuries?.join(', ') || 'None'}

${data.sharpLines ? `SHARP LINES:
${data.sharpLines.map(line => `${line.bookmaker}: ${line.line} (${line.edge_percentage.toFixed(1)}% edge)`).join('\n')}` : ''}

RESEARCH TASKS:
1. Analyze player's recent form and season trends
2. Evaluate matchup advantages/disadvantages  
3. Assess line value vs market consensus
4. Identify key risk factors and variance
5. Make final recommendation with confidence level

Respond in JSON format:
{
  "research_summary": "Comprehensive research findings in 2-3 paragraphs",
  "decision": "OVER" | "UNDER" | "AVOID",
  "confidence": 1-5,
  "key_factors": ["Primary factor", "Secondary factor", "Tertiary factor"],
  "edge_assessment": "Line value analysis vs sharp books",
  "risk_factors": ["Risk 1", "Risk 2"],
  "supporting_data": {
    "recent_average": 0.0,
    "vs_opponent_history": 0.0,
    "line_edge_percentage": 0.0,
    "injury_impact": "none|minor|moderate|major"
  }
}`

  try {
    const response = await anthropic.messages.create({
      model: 'claude-sonnet-4-20250514',
      max_tokens: 1500,
      temperature: 0.2,
      messages: [{ role: 'user', content: prompt }]
    })

    const analysisText = response.content[0]?.type === 'text' ? response.content[0].text : ''
    return JSON.parse(analysisText)
    
  } catch (error) {
    console.error('Claude research error:', error)
    throw error
  }
}

// Stage 2: Cheaper AI for breakdown formatting
async function generateBreakdown(
  researchResult: ResearchDecisionResult, 
  propData: any,
  useGroq = false
): Promise<BreakdownResult> {
  
  const client = useGroq ? groq : deepseek
  const model = useGroq ? 'llama-3.1-70b-versatile' : 'deepseek-chat'
  
  const prompt = `Create a detailed breakdown for this sports betting analysis.

RESEARCH FINDINGS: ${researchResult.research_summary}
DECISION: ${researchResult.decision} (Confidence: ${researchResult.confidence}/5)
KEY FACTORS: ${researchResult.key_factors.join(', ')}
EDGE ASSESSMENT: ${researchResult.edge_assessment}
RISKS: ${researchResult.risk_factors.join(', ')}

PROP DETAILS:
- Player: ${propData.player.name}
- Bet: ${propData.prop.type} ${propData.prop.line}
- Team: ${propData.player.team} vs ${propData.opponent.name}

Create a professional breakdown in JSON format:
{
  "analysis_text": "4-5 paragraph detailed analysis incorporating all research findings",
  "reasoning_points": [
    "Point 1 with specific data",
    "Point 2 with matchup details", 
    "Point 3 with line value assessment",
    "Point 4 with risk considerations"
  ],
  "edge_summary": "Concise summary of betting edge and value",
  "correlations": ["Related prop 1", "Related prop 2"],
  "formatted_breakdown": "Clean, readable summary for display"
}`

  try {
    const response = await client.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      max_tokens: 1200,
      ...(useGroq ? {} : { response_format: { type: 'json_object' } })
    })

    const content = response.choices[0]?.message?.content || '{}'
    
    // Handle potential non-JSON response from Groq
    let result
    try {
      result = JSON.parse(content)
    } catch {
      // Fallback parsing for non-JSON responses
      result = {
        analysis_text: content,
        reasoning_points: researchResult.key_factors,
        edge_summary: researchResult.edge_assessment,
        correlations: [],
        formatted_breakdown: content.substring(0, 500) + '...'
      }
    }
    
    return result
    
  } catch (error) {
    console.error('Breakdown generation error:', error)
    // Fallback to basic formatting
    return {
      analysis_text: researchResult.research_summary,
      reasoning_points: researchResult.key_factors,
      edge_summary: researchResult.edge_assessment,
      correlations: [],
      formatted_breakdown: `${researchResult.decision}: ${researchResult.research_summary}`
    }
  }
}

// Main pipeline function
export async function pipelineAnalysis(data: any, options = { useGroq: false }) {
  console.log(`Pipeline analysis: ${data.player.name} ${data.prop.type}`)
  
  try {
    // Stage 1: Claude research and decision (high cost, high accuracy)
    const researchResult = await claudeResearchAndDecision(data)
    
    // Stage 2: Cheaper AI for breakdown (low cost, good formatting)
    const breakdownResult = await generateBreakdown(researchResult, data, options.useGroq)
    
    // Combine results
    return {
      analysis_text: breakdownResult.analysis_text,
      confidence_rating: researchResult.confidence,
      recommendation: researchResult.decision,
      reasoning_points: breakdownResult.reasoning_points,
      edge_summary: breakdownResult.edge_summary,
      correlations: breakdownResult.correlations,
      research_summary: researchResult.research_summary,
      supporting_data: researchResult.supporting_data,
      cost_estimate: 0.15 + (options.useGroq ? 0.001 : 0.003), // Claude + Groq/DeepSeek
      analysis_method: `Claude Research + ${options.useGroq ? 'Groq' : 'DeepSeek'} Breakdown`
    }
    
  } catch (error) {
    console.error('Pipeline analysis error:', error)
    throw error
  }
}

// Batch processing with pipeline approach
export async function batchPipelineAnalysis(dataArray: any[], options = { useGroq: false, maxConcurrent: 5 }) {
  const results = []
  
  // Process in smaller batches to manage costs
  for (let i = 0; i < dataArray.length; i += options.maxConcurrent) {
    const batch = dataArray.slice(i, i + options.maxConcurrent)
    
    const batchPromises = batch.map(async (data, index) => {
      try {
        // Stagger requests to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, index * 300))
        return await pipelineAnalysis(data, options)
      } catch (error) {
        console.error(`Pipeline analysis failed for ${data.player.name}:`, error)
        return {
          analysis_text: 'Pipeline analysis failed',
          confidence_rating: 1,
          recommendation: 'AVOID',
          reasoning_points: ['Analysis error'],
          cost_estimate: 0,
          analysis_method: 'Error'
        }
      }
    })
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
    
    // Longer delay between batches
    if (i + options.maxConcurrent < dataArray.length) {
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }
  
  const totalCost = results.reduce((sum, r) => sum + (r.cost_estimate || 0), 0)
  console.log(`Pipeline batch complete: ${results.length} analyses, $${totalCost.toFixed(2)} estimated cost`)
  
  return results
}
