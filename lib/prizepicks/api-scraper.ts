/**
 * PrizePicks API Scraper
 * Automatically fetches current props from PrizePicks API
 */

interface PrizePicksPlayer {
  type: string
  id: string
  attributes: {
    combo: boolean
    display_name: string
    image_url: string
    jersey_number: string
    league: string
    league_id: number
    market: string
    name: string
    position: string
    team: string
    team_name: string
  }
  relationships: {
    league: { data: { type: string; id: string } }
    team_data: { data: { type: string; id: string } }
  }
}

interface PrizePicksProp {
  type: string
  id: string
  attributes: {
    board_time: string
    description: string
    display_name: string
    game_id: number
    league: string
    line_score: number
    odds_type: string
    pick_type: string
    player_id: number
    position: string
    projection_type: string
    start_time: string
    stat_type: string
    team: string
    team_name: string
  }
  relationships: {
    new_player: { data: { type: string; id: string } }
    league: { data: { type: string; id: string } }
  }
}

interface PrizePicksAPIResponse {
  data: PrizePicksProp[]
  included: PrizePicksPlayer[]
  meta: {
    total_count: number
  }
}

// League mappings
const LEAGUE_MAPPINGS: Record<string, string> = {
  'NFL': 'NFL',
  'NCAAF': 'NCAAF', 
  'WNBA': 'WNBA',
  'MLB': 'MLB',
  'NBA': 'NBA',
  'SOCCER': 'SOCCER'
}

// Stat type mappings to our prop types
const STAT_TYPE_MAPPINGS: Record<string, string> = {
  // NFL
  'Passing Yards': 'passing_yards',
  'Rushing Yards': 'rushing_yards',
  'Receiving Yards': 'receiving_yards',
  'Pass TDs': 'passing_touchdowns',
  'Rush TDs': 'rushing_touchdowns',
  'Receptions': 'receptions',
  'Rush+Rec TDs': 'rush_rec_touchdowns',
  
  // Basketball (NBA/WNBA)
  'Points': 'points',
  'Rebounds': 'rebounds',
  'Assists': 'assists',
  'Threes Made': 'three_pointers_made',
  'Steals': 'steals',
  'Blocks': 'blocks',
  
  // MLB
  'Hits': 'hits',
  'RBIs': 'runs_batted_in',
  'Runs': 'runs_scored',
  'Home Runs': 'home_runs',
  'Strikeouts': 'pitcher_strikeouts',
  'Walks': 'walks_allowed',
  
  // Soccer
  'Shots': 'shots',
  'Goals': 'goals',
  'Assists': 'assists'
}

/**
 * Fetch current projections from PrizePicks API
 */
export async function fetchPrizePicksProjections(leagues: string[] = ['NFL', 'WNBA', 'NCAAF', 'MLB']): Promise<PrizePicksAPIResponse> {
  try {
    // PrizePicks API endpoint for projections
    const url = 'https://api.prizepicks.com/projections'
    
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })

    if (!response.ok) {
      throw new Error(`PrizePicks API error: ${response.status} ${response.statusText}`)
    }

    const data: PrizePicksAPIResponse = await response.json()
    
    // Filter by requested leagues
    const filteredData = {
      ...data,
      data: data.data.filter(prop => leagues.includes(prop.attributes.league))
    }

    console.log(`📊 Fetched ${filteredData.data.length} props from PrizePicks API`)
    return filteredData

  } catch (error) {
    console.error('Failed to fetch PrizePicks projections:', error)
    throw error
  }
}

/**
 * Convert PrizePicks API data to our database format
 */
export function convertPrizePicksData(apiData: PrizePicksAPIResponse) {
  const players = new Map<string, PrizePicksPlayer>()
  
  // Index players by ID
  apiData.included?.forEach(player => {
    if (player.type === 'new_player') {
      players.set(player.id, player)
    }
  })

  const props = apiData.data.map(prop => {
    const player = players.get(prop.relationships.new_player.data.id)
    
    if (!player) {
      console.warn(`Player not found for prop ${prop.id}`)
      return null
    }

    // Map stat type to our prop type
    const propType = STAT_TYPE_MAPPINGS[prop.attributes.stat_type] || 
                    prop.attributes.stat_type.toLowerCase().replace(/\s+/g, '_')

    return {
      external_id: `pp_${prop.id}`,
      sport: LEAGUE_MAPPINGS[prop.attributes.league] || prop.attributes.league,
      player_name: player.attributes.display_name,
      team: player.attributes.team,
      opponent: null, // We'll need to derive this from game data
      prop_type: propType,
      line_value: prop.attributes.line_score,
      game_time: prop.attributes.start_time,
      is_active: true,
      scraped_at: new Date().toISOString(),
      // Additional metadata
      position: player.attributes.position,
      jersey_number: player.attributes.jersey_number,
      player_image_url: player.attributes.image_url,
      game_id: prop.attributes.game_id,
      pick_type: prop.attributes.pick_type,
      odds_type: prop.attributes.odds_type
    }
  }).filter(Boolean)

  return props
}

/**
 * Get opponent information by grouping props by game_id
 */
export function enrichWithOpponents(props: any[]) {
  // Group props by game_id to determine opponents
  const gameGroups = new Map<number, any[]>()
  
  props.forEach(prop => {
    if (!gameGroups.has(prop.game_id)) {
      gameGroups.set(prop.game_id, [])
    }
    gameGroups.get(prop.game_id)!.push(prop)
  })

  // For each game, determine the two teams and set opponents
  gameGroups.forEach(gameProps => {
    const teams = [...new Set(gameProps.map(p => p.team))]
    
    if (teams.length === 2) {
      gameProps.forEach(prop => {
        prop.opponent = teams.find(team => team !== prop.team)
      })
    }
  })

  return props
}

/**
 * Main function to scrape and format PrizePicks data
 */
export async function scrapePrizePicksData(leagues: string[] = ['NFL', 'WNBA', 'NCAAF', 'MLB']) {
  try {
    console.log(`🔄 Scraping PrizePicks data for leagues: ${leagues.join(', ')}`)
    
    // Fetch data from API
    const apiData = await fetchPrizePicksProjections(leagues)
    
    // Convert to our format
    let props = convertPrizePicksData(apiData)
    
    // Enrich with opponent data
    props = enrichWithOpponents(props)
    
    console.log(`✅ Successfully processed ${props.length} props`)
    
    return {
      props,
      metadata: {
        scraped_at: new Date().toISOString(),
        total_count: apiData.meta?.total_count || props.length,
        leagues_requested: leagues,
        api_source: 'prizepicks'
      }
    }

  } catch (error) {
    console.error('PrizePicks scraping failed:', error)
    throw error
  }
}
