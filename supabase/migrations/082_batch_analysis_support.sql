-- Batch Analysis Support Migration
-- Adds columns needed for the new batch analysis system

-- Add additional columns to ai_analyses table for enhanced analysis
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS analysis_method text DEFAULT 'claude_individual';
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS cost_estimate decimal(8,4) DEFAULT 0;
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS cache_key text;
ALTER TABLE ai_analyses ADD COLUMN IF NOT EXISTS batch_id uuid;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_analyses_analysis_method ON ai_analyses(analysis_method);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_cost_estimate ON ai_analyses(cost_estimate);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_cache_key ON ai_analyses(cache_key) WHERE cache_key IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_ai_analyses_batch_id ON ai_analyses(batch_id) WHERE batch_id IS NOT NULL;

-- Add composite index for prop analysis lookups
CREATE INDEX IF NOT EXISTS idx_ai_analyses_prop_recent ON ai_analyses(prop_id, created_at DESC);

-- Update RLS policies to allow service role to insert batch analyses
-- Drop existing policies if they exist and recreate them
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Allow service batch analyses" ON ai_analyses;
  DROP POLICY IF EXISTS "Allow service update analyses" ON ai_analyses;

  -- Create new policies
  CREATE POLICY "Allow service batch analyses" ON ai_analyses
    FOR INSERT TO service_role WITH CHECK (true);

  CREATE POLICY "Allow service update analyses" ON ai_analyses
    FOR UPDATE TO service_role USING (true);
EXCEPTION
  WHEN others THEN
    -- Policies might not exist, continue
    NULL;
END $$;

-- Function to get analysis statistics
CREATE OR REPLACE FUNCTION get_analysis_stats(
  start_date timestamptz DEFAULT now() - interval '24 hours',
  end_date timestamptz DEFAULT now()
)
RETURNS TABLE (
  total_analyses bigint,
  claude_analyses bigint,
  deepseek_analyses bigint,
  batch_analyses bigint,
  total_cost decimal,
  avg_cost_per_analysis decimal,
  avg_confidence decimal
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_analyses,
    COUNT(*) FILTER (WHERE analysis_method LIKE '%claude%') as claude_analyses,
    COUNT(*) FILTER (WHERE analysis_method LIKE '%deepseek%') as deepseek_analyses,
    COUNT(*) FILTER (WHERE batch_id IS NOT NULL) as batch_analyses,
    COALESCE(SUM(cost_estimate), 0) as total_cost,
    COALESCE(AVG(cost_estimate), 0) as avg_cost_per_analysis,
    COALESCE(AVG(confidence_rating::decimal), 0) as avg_confidence
  FROM ai_analyses
  WHERE created_at >= start_date AND created_at <= end_date;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_analysis_stats TO authenticated;

-- Function to clean up old cache keys
CREATE OR REPLACE FUNCTION cleanup_stale_cache_keys()
RETURNS void AS $$
BEGIN
  -- Remove cache keys for analyses older than 24 hours
  UPDATE ai_analyses 
  SET cache_key = NULL 
  WHERE cache_key IS NOT NULL 
    AND created_at < now() - interval '24 hours';
    
  -- Log cleanup
  RAISE NOTICE 'Cleaned up stale cache keys';
END;
$$ LANGUAGE plpgsql;

-- Add to existing cleanup function
CREATE OR REPLACE FUNCTION cleanup_expired_analyses()
RETURNS void AS $$
BEGIN
  -- Delete expired analyses
  DELETE FROM ai_analyses WHERE expires_at < now();
  
  -- Clean up stale cache keys
  PERFORM cleanup_stale_cache_keys();
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the new columns
COMMENT ON COLUMN ai_analyses.analysis_method IS 'Method used for analysis: claude_individual, claude_batch, deepseek, tiered_claude, tiered_deepseek, etc.';
COMMENT ON COLUMN ai_analyses.cost_estimate IS 'Estimated cost in USD for this analysis';
COMMENT ON COLUMN ai_analyses.cache_key IS 'Cache key for identifying similar analyses';
COMMENT ON COLUMN ai_analyses.batch_id IS 'ID linking analyses that were processed together in a batch';

-- Create view for analysis performance metrics (drop and recreate to avoid conflicts)
DROP VIEW IF EXISTS analysis_performance_metrics;
CREATE VIEW analysis_performance_metrics AS
SELECT 
  DATE(created_at) as analysis_date,
  analysis_method,
  COUNT(*) as analysis_count,
  AVG(confidence_rating) as avg_confidence,
  SUM(cost_estimate) as total_cost,
  AVG(cost_estimate) as avg_cost,
  COUNT(DISTINCT batch_id) FILTER (WHERE batch_id IS NOT NULL) as batch_count
FROM ai_analyses
WHERE created_at >= now() - interval '30 days'
GROUP BY DATE(created_at), analysis_method
ORDER BY analysis_date DESC, analysis_method;

-- Grant access to the view
GRANT SELECT ON analysis_performance_metrics TO authenticated;
