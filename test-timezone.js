// Quick test for timezone parsing
function parseGameTime(dayOfWeek, timeStr) {
  // Get current time in Eastern timezone
  const now = new Date()
  const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.

  // Map day names to numbers
  const dayMap = {
    'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 'Fri': 5, 'Sat': 6
  }

  const targetDay = dayMap[dayOfWeek]
  if (targetDay === undefined) {
    // Default to tomorrow 8 PM ET if we can't parse the day
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(20, 0, 0, 0)
    return tomorrow.toISOString()
  }

  // Parse time
  const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})(am|pm)/)
  if (!timeMatch) {
    // Default time if we can't parse
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(20, 0, 0, 0)
    return tomorrow.toISOString()
  }

  let hours = parseInt(timeMatch[1])
  const minutes = parseInt(timeMatch[2])
  const ampm = timeMatch[3]

  if (ampm === 'pm' && hours !== 12) hours += 12
  if (ampm === 'am' && hours === 12) hours = 0

  // Calculate days until target day
  let daysUntil = targetDay - currentDay

  // If it's the same day, check if the time has passed
  if (daysUntil === 0) {
    const gameTimeToday = new Date()
    gameTimeToday.setHours(hours, minutes, 0, 0)
    if (gameTimeToday <= now) {
      daysUntil = 7 // Next week
    }
  } else if (daysUntil < 0) {
    daysUntil += 7 // Next week
  }

  // Create the game date and time
  const gameDate = new Date()
  gameDate.setDate(gameDate.getDate() + daysUntil)

  // Set the time in Eastern timezone by creating a date string and parsing it
  const year = gameDate.getFullYear()
  const month = String(gameDate.getMonth() + 1).padStart(2, '0')
  const day = String(gameDate.getDate()).padStart(2, '0')
  const hour = String(hours).padStart(2, '0')
  const minute = String(minutes).padStart(2, '0')

  // Create date string in Eastern timezone format
  // Use a more reliable method to handle Eastern time
  const easternDateStr = `${year}-${month}-${day} ${hour}:${minute}:00`

  // Parse as if it's in Eastern timezone
  // Create a temporary date to check DST
  const tempDate = new Date(`${year}-${month}-${day}T12:00:00`)
  const isWinter = tempDate.getMonth() < 3 || tempDate.getMonth() > 10 // Rough DST check
  const offsetHours = isWinter ? 5 : 4 // EST = UTC-5, EDT = UTC-4

  // Create the final date by parsing the Eastern time and converting to UTC
  const easternDate = new Date(`${easternDateStr} GMT-0${offsetHours}00`)

  return easternDate.toISOString()
}

// Test the function
console.log('Testing timezone parsing:')
console.log('Tue 9:30pm ->', parseGameTime('Tue', '9:30pm'))
console.log('Tue 7:30pm ->', parseGameTime('Tue', '7:30pm'))

// Test with our timezone utility
function formatGameTime(gameTime) {
  const date = typeof gameTime === 'string' ? new Date(gameTime) : gameTime
  
  if (isNaN(date.getTime())) {
    return 'TBD'
  }

  const easternTime = date.toLocaleString("en-US", {
    timeZone: "America/New_York",
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })

  return easternTime + ' EST'
}

const testTime1 = parseGameTime('Tue', '9:30pm')
const testTime2 = parseGameTime('Tue', '7:30pm')

console.log('Formatted times:')
console.log('9:30pm ->', formatGameTime(testTime1))
console.log('7:30pm ->', formatGameTime(testTime2))
