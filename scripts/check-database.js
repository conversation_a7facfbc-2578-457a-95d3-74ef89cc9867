#!/usr/bin/env node

// Simple script to check what's in the database
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables')
  console.error('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl)
  console.error('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseKey)
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkDatabase() {
  console.log('🔍 Checking PrizePicks database...')
  
  try {
    // Check total count
    const { count, error: countError } = await supabase
      .from('prizepicks_props')
      .select('*', { count: 'exact', head: true })
    
    if (countError) {
      console.error('Count error:', countError)
      return
    }
    
    console.log(`📊 Total props in database: ${count}`)
    
    if (count === 0) {
      console.log('❌ No props found in database')
      return
    }
    
    // Get sample data
    const { data: props, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, prop_type, is_active, created_at')
      .order('created_at', { ascending: false })
      .limit(10)
    
    if (fetchError) {
      console.error('Fetch error:', fetchError)
      return
    }
    
    console.log('\n📋 Sample props:')
    props.forEach((prop, i) => {
      console.log(`${i + 1}. ${prop.player_name} (${prop.team}) - ${prop.sport} - ${prop.prop_type} - Active: ${prop.is_active}`)
    })
    
    // Count by sport
    const { data: sportCounts, error: sportError } = await supabase
      .from('prizepicks_props')
      .select('sport')
    
    if (!sportError && sportCounts) {
      const counts = {}
      sportCounts.forEach(prop => {
        counts[prop.sport] = (counts[prop.sport] || 0) + 1
      })
      
      console.log('\n🏆 Props by sport:')
      Object.entries(counts).forEach(([sport, count]) => {
        console.log(`  ${sport}: ${count}`)
      })
    }
    
    // Check for MLB props marked as NFL
    const { data: nflProps, error: nflError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, prop_type')
      .eq('sport', 'NFL')
      .limit(20)
    
    if (!nflError && nflProps) {
      console.log('\n🏈 Props marked as NFL:')
      nflProps.forEach(prop => {
        const isMLB = ['hits', 'rbis', 'runs', 'strikeouts', 'total_bases'].includes(prop.prop_type)
        const mlbTeams = ['DET', 'CLE', 'BAL', 'BOS', 'NYY', 'TB', 'TOR', 'MIN', 'KC', 'HOU', 'LAA', 'OAK', 'SEA', 'TEX']
        const isMLBTeam = mlbTeams.includes(prop.team)
        
        if (isMLB || isMLBTeam) {
          console.log(`  ⚠️  ${prop.player_name} (${prop.team}) - ${prop.prop_type} - LIKELY MLB!`)
        } else {
          console.log(`  ✅ ${prop.player_name} (${prop.team}) - ${prop.prop_type}`)
        }
      })
    }
    
  } catch (error) {
    console.error('Database check failed:', error)
  }
}

checkDatabase()
