# Batch Analysis System - Production Deployment Guide

## 🚀 System Overview

The new batch analysis system provides:
- **50-70% cost reduction** through intelligent batching
- **Claude + DeepSeek pipeline** for optimal quality/cost balance
- **Enhanced breakdown popups** with structured analysis
- **Production-ready error handling** and fallbacks

## 📋 Pre-Deployment Checklist

### 1. Environment Variables
Ensure these are set in production:
```bash
ANTHROPIC_API_KEY=your_claude_key
DEEPSEEK_API_KEY=your_deepseek_key
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### 2. Database Migration
Run the new migration:
```sql
-- Apply migration 082_batch_analysis_support.sql
-- This adds required columns to ai_analyses table
```

### 3. API Keys & Rate Limits
- **Claude Sonnet 4**: Ensure sufficient rate limits for batch requests
- **DeepSeek**: Verify API access and quotas
- **Test both APIs** with the test script

### 4. Test the System
```bash
# Run the API test script
node scripts/test-batch-api.js

# Expected output:
# 🎉 ALL TESTS PASSED!
# 🚀 System is ready for production deployment!
```

## 🔄 Migration Path

### Phase 1: Parallel Deployment
1. Deploy new batch analysis API (`/api/prizepicks/batch-analyze-v2`)
2. Keep existing system running (`/api/prizepicks/bulk-analyze`)
3. Test with small batches

### Phase 2: Frontend Update
1. Update input page to use v2 API
2. Enhanced breakdown popups
3. Monitor performance and costs

### Phase 3: Full Cutover
1. Switch all batch operations to v2
2. Remove old bulk-analyze endpoint
3. Monitor and optimize

## 📊 Expected Performance

### Cost Comparison (200 props/day)
| Method | Daily Cost | Monthly Cost | Quality |
|--------|------------|--------------|---------|
| **Old (Claude only)** | $9.00 | $270 | Excellent |
| **New (Batch)** | $3.50 | $105 | Excellent |
| **Savings** | $5.50 | $165 | Same |

### Processing Time
- **Individual**: ~200 props × 3s = 10 minutes
- **Batch**: ~40 batches × 5s = 3.5 minutes
- **Improvement**: 65% faster

## 🛠 API Endpoints

### 1. Batch Analysis v2
```
POST /api/prizepicks/batch-analyze-v2
{
  "maxAnalyses": 200,
  "forceRefresh": false
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "analyzed": 150,
    "cached": 50,
    "failed": 0,
    "total_cost": 3.45,
    "claude_cost": 2.80,
    "deepseek_cost": 0.65,
    "average_cost_per_prop": 0.023,
    "processing_time_ms": 45000
  }
}
```

### 2. Enhanced Breakdown
```
POST /api/prizepicks/breakdown
{
  "propId": "uuid"
}
```

**Response:**
```json
{
  "prop_id": "uuid",
  "recommendation": "OVER",
  "confidence_rating": 4,
  "breakdown_sections": {
    "executive_summary": "...",
    "key_factors": ["...", "...", "..."],
    "pros": ["...", "...", "..."],
    "cons": ["...", "...", "..."],
    "risk_assessment": "...",
    "final_verdict": "..."
  }
}
```

## 🔍 Monitoring & Alerts

### Key Metrics to Track
1. **Cost per analysis** (target: <$0.025)
2. **Processing time** (target: <5 minutes for 200 props)
3. **Error rate** (target: <2%)
4. **Cache hit rate** (target: >30%)

### Database Queries for Monitoring
```sql
-- Daily cost analysis
SELECT 
  DATE(created_at) as date,
  COUNT(*) as analyses,
  SUM(cost_estimate) as total_cost,
  AVG(cost_estimate) as avg_cost,
  analysis_method
FROM ai_analyses 
WHERE created_at >= now() - interval '7 days'
GROUP BY DATE(created_at), analysis_method
ORDER BY date DESC;

-- Performance metrics
SELECT * FROM analysis_performance_metrics 
WHERE analysis_date >= current_date - 7;

-- Error tracking
SELECT 
  COUNT(*) as failed_analyses,
  DATE(created_at) as date
FROM ai_analyses 
WHERE recommendation = 'AVOID' 
  AND analysis_text LIKE '%failed%'
  AND created_at >= now() - interval '24 hours'
GROUP BY DATE(created_at);
```

## 🚨 Troubleshooting

### Common Issues

1. **High Costs**
   - Check if batching is working (props should be grouped by game)
   - Verify DeepSeek is being used for breakdowns
   - Monitor `analysis_method` column

2. **Slow Processing**
   - Check API rate limits
   - Verify batch sizes (should be 3-5 props per game)
   - Monitor network latency

3. **Analysis Failures**
   - Check API keys and quotas
   - Verify JSON parsing in responses
   - Check fallback mechanisms

### Emergency Rollback
If issues occur, quickly rollback:
```bash
# Revert frontend to use old API
# Change /api/prizepicks/batch-analyze-v2 back to /api/prizepicks/bulk-analyze
```

## 📈 Optimization Opportunities

### Short Term
1. **Smart caching** based on line movements
2. **Priority scoring** refinement
3. **Batch size optimization** per sport

### Long Term
1. **Real-time analysis** for line movements
2. **Multi-model ensemble** for higher confidence
3. **Predictive caching** for popular props

## ✅ Go-Live Checklist

- [ ] Environment variables configured
- [ ] Database migration applied
- [ ] Test script passes
- [ ] API keys verified
- [ ] Monitoring dashboards ready
- [ ] Rollback plan documented
- [ ] Team trained on new system

## 🎯 Success Criteria

After 1 week of production use:
- [ ] Cost reduction of 50%+ achieved
- [ ] Processing time improved by 60%+
- [ ] Error rate below 2%
- [ ] User satisfaction maintained
- [ ] No performance degradation

---

**Ready for production deployment! 🚀**

The batch analysis system is designed for reliability, cost-efficiency, and scalability. Monitor the key metrics and adjust batch sizes as needed for optimal performance.
