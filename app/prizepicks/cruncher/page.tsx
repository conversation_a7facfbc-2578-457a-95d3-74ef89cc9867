'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { formatDistanceToNow } from 'date-fns'
import { formatGameTime } from '@/lib/utils/timezone'

const supabase = createSupabaseClient()

interface Prop {
  id: string
  external_id: string
  sport: string
  player_name: string
  team?: string
  opponent?: string
  prop_type: string
  line_value: number
  game_time: string
  is_active: boolean
  scraped_at: string
  movement_count?: number
  last_movement_at?: string
  biggest_movement?: number
  ai_analysis?: {
    analysis_text: string
    confidence_rating: number
    recommendation: 'OVER' | 'UNDER' | 'AVOID'
    reasoning_points: string[]
    created_at: string
  }
}

const SPORT_EMOJIS = {
  'NFL': '🏈',
  'NCAAF': '🏈',
  'Tennis': '🎾',
  'WNBA': '🏀',
  'NBA': '🏀',
  'MLB': '⚾'
}

const PROP_TYPE_LABELS = {
  // NFL
  'passing_yards': 'Pass Yds',
  'rushing_yards': 'Rush Yds',
  'receiving_yards': 'Rec Yds',
  'receptions': 'Receptions',
  'passing_touchdowns': 'Pass TDs',
  'rushing_touchdowns': 'Rush TDs',
  'receiving_touchdowns': 'Rec TDs',
  'rush_rec_touchdowns': 'Rush+Rec TDs',
  'rush_attempts': 'Rush Att',
  'pass_attempts': 'Pass Att',
  'tackles_assists': 'Tackles',
  'sacks': 'Sacks',
  'interceptions': 'INTs',
  'field_goals_made': 'FG Made',
  'longest_reception': 'Long Rec',
  'completion_percentage': 'Comp %',

  // WNBA/NBA
  'points': 'Points',
  'rebounds': 'Rebounds',
  'assists': 'Assists',
  'points_rebounds_assists': 'Pts+Reb+Ast',
  'three_pointers_made': '3PM',
  'points_assists': 'Pts+Ast',
  'points_rebounds': 'Pts+Reb',
  'rebounds_assists': 'Reb+Ast',
  'steals': 'Steals',
  'blocks': 'Blocks',
  'turnovers': 'TOs',
  'double_double': 'Double-Double',
  'triple_double': 'Triple-Double',

  // MLB
  'hits': 'Hits',
  'total_bases': 'Total Bases',
  'runs_batted_in': 'RBIs',
  'runs_scored': 'Runs',
  'home_runs': 'HRs',
  'strikeouts': 'Ks',
  'walks': 'BBs',
  'stolen_bases': 'SBs',
  'hits_runs_rbis': 'H+R+RBI',
  'pitcher_wins': 'Wins',
  'innings_pitched': 'IP',
  'earned_runs_allowed': 'ER',

  // Tennis
  'aces': 'Aces',
  'sets_won': 'Sets Won'
}

function getSportEmoji(sport: string) {
  return SPORT_EMOJIS[sport as keyof typeof SPORT_EMOJIS] || ''
}

function getPropTypeLabel(propType: string) {
  return PROP_TYPE_LABELS[propType as keyof typeof PROP_TYPE_LABELS] || propType
}

export default function PrizePicksCruncher() {
  const [props, setProps] = useState<Prop[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [authLoading, setAuthLoading] = useState(true)
  const [selectedSport, setSelectedSport] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('smart')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [displayLimit, setDisplayLimit] = useState<number>(100)
  const [totalAvailable, setTotalAvailable] = useState<number>(0)
  const [analyzingProps, setAnalyzingProps] = useState<Set<string>>(new Set())
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [breakdownProp, setBreakdownProp] = useState<Prop | null>(null)
  const [breakdownData, setBreakdownData] = useState<any>(null)
  const [breakdownLoading, setBreakdownLoading] = useState(false)

  const [nowMs, setNowMs] = useState<number>(Date.now())

  // Schedule a one-time update at the next game start time to drop started games
  useEffect(() => {
    if (!props || props.length === 0) return
    const now = Date.now()
    const upcoming = props
      .map(p => new Date(p.game_time).getTime())
      .filter(t => t > now)
    if (upcoming.length === 0) return
    const next = Math.min(...upcoming)
    const delay = Math.max(0, next - now + 1000) // +1s buffer
    const id = setTimeout(async () => {
      setNowMs(Date.now()) // trigger re-render so visibleProps recalculates
      try {
        await fetch('/api/prizepicks/cleanup', { method: 'POST' })
      } catch (e) {
        console.warn('Cleanup trigger failed (non-fatal):', e)
      }
    }, delay)
    return () => clearTimeout(id)
  }, [props])

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        // Redirect to login with return URL
        window.location.href = `/login?next=${encodeURIComponent('/prizepicks/cruncher')}`
        return
      }

      setUser(user)
      setAuthLoading(false)
    }

    checkAuth()
  }, [])

  // Load props from database
  const loadProps = async () => {
    try {
      setLoading(true)

      let query = supabase
        .from('prizepicks_props')
        .select('*, ai_analyses(analysis_text,confidence_rating,recommendation,reasoning_points,created_at)')
        .eq('is_active', true)
        .gt('game_time', new Date().toISOString())

      if (selectedSport !== 'all') {
        query = query.eq('sport', selectedSport)
      }

      // Smart sorting: AI confidence first, then game time for optimal betting strategy
      if (sortBy === 'smart') {
        // Best picks first: AI confidence DESC, then game time ASC (soonest games within each rating)
        query = query.order('ai_analyses.confidence_rating', { ascending: false, nullsLast: true })
                    .order('game_time', { ascending: true })
      } else if (sortBy === 'game_time') {
        query = query.order('game_time', { ascending: sortOrder === 'asc' })
      } else if (sortBy === 'confidence') {
        query = query.order('ai_analyses.confidence_rating', { ascending: sortOrder === 'asc', nullsLast: true })
                    .order('game_time', { ascending: true })
      } else if (sortBy === 'movement') {
        query = query.order('movement_count', { ascending: sortOrder === 'asc', nullsLast: true })
                    .order('game_time', { ascending: true })
      } else {
        // Fallback to smart sorting
        query = query.order('ai_analyses.confidence_rating', { ascending: false, nullsLast: true })
                    .order('game_time', { ascending: true })
      }

      const { data, error } = await query.limit(500)

      if (error) {
        console.error('Error loading props:', error)
        return
      }

      const formattedProps = data?.map(prop => ({
        ...prop,
        ai_analysis: prop.ai_analyses?.[0], // Get most recent analysis
        movement_count: 0, // Default since we're not using the view
        last_movement_at: null,
        biggest_movement: null
      })) || []

      setProps(formattedProps)
      setTotalAvailable(formattedProps.length)
      setLastUpdate(new Date())

      // Reset display limit when filters change
      if (displayLimit > 100) {
        setDisplayLimit(100)
      }

    } catch (error) {
      console.error('Failed to load props:', error)
    } finally {
      setLoading(false)
    }
  }

  // Request AI analysis for a prop
  const analyzeProps = async (propId: string) => {
    if (analyzingProps.has(propId)) return

    setAnalyzingProps(prev => new Set(prev).add(propId))

    try {
      const response = await fetch('/api/prizepicks/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ propId })
      })

      if (!response.ok) {
        throw new Error('Analysis failed')
      }

      const analysis = await response.json()

      // Update the prop with new analysis
      setProps(prev => prev.map(prop =>
        prop.id === propId
          ? { ...prop, ai_analysis: analysis }
          : prop
      ))

    } catch (error) {
      console.error('Analysis error:', error)
    } finally {
      setAnalyzingProps(prev => {
        const newSet = new Set(prev)
        newSet.delete(propId)
        return newSet
      })
    }
  }

  // Handle breakdown click with enhanced API
  const handleBreakdownClick = async (prop: Prop) => {
    setBreakdownProp(prop)
    setBreakdownLoading(true)
    setBreakdownData(null)

    try {
      const response = await fetch('/api/prizepicks/breakdown', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ propId: prop.id })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate breakdown')
      }

      const data = await response.json()
      setBreakdownData(data)

    } catch (error) {
      console.error('Breakdown error:', error)
      // Fallback to basic breakdown using existing analysis
      setBreakdownData({
        breakdown_sections: {
          executive_summary: `${prop.ai_analysis?.recommendation} recommendation for ${prop.player_name} ${prop.prop_type} ${prop.line_value}`,
          key_factors: prop.ai_analysis?.reasoning_points || ['Analysis available in main view'],
          pros: [`Recommendation: ${prop.ai_analysis?.recommendation}`, 'Based on comprehensive analysis'],
          cons: ['Standard betting risks apply', 'Market conditions may change'],
          risk_assessment: 'Standard variance and risk factors apply to this bet.',
          final_verdict: `${prop.ai_analysis?.recommendation} with ${prop.ai_analysis?.confidence_rating}/5 confidence based on available data.`
        }
      })
    } finally {
      setBreakdownLoading(false)
    }
  }

  // Auto-refresh every 3 hours (only when authenticated)
  useEffect(() => {
    if (!authLoading && user) {
      loadProps()
      const interval = setInterval(loadProps, 3 * 60 * 60 * 1000) // 3 hours
      return () => clearInterval(interval)
    }
  }, [selectedSport, sortBy, sortOrder, authLoading, user])

  // Manual refresh
  const handleRefresh = async () => {
    // Trigger scraping then cleanup/archive started games
    try {
      await fetch('/api/prizepicks/scrape', { method: 'POST' })
      await fetch('/api/prizepicks/cleanup', { method: 'POST' })
      setTimeout(loadProps, 2000) // Wait briefly for DB updates to settle
    } catch (error) {
      console.error('Refresh failed:', error)
    }
  }

  const getConfidenceDisplay = (rating: number) => {
    const fire = '🔥'.repeat(rating)
    const empty = '⚪'.repeat(5 - rating)
    return fire + empty
  }

  const getRecommendationStyle = (rec: string) => {
    switch (rec) {
      case 'OVER': return 'text-green-600 bg-green-50 border-green-200'
      case 'UNDER': return 'text-red-600 bg-red-50 border-red-200'
      case 'AVOID': return 'text-gray-600 bg-gray-50 border-gray-200'
      default: return 'text-gray-600'
    }
  }

  const getMovementIndicator = (prop: Prop) => {
    if (!prop.movement_count || prop.movement_count === 0) return null

    const movement = prop.biggest_movement || 0
    if (movement > 0) {
      return <span className="text-green-600 text-xs">↗ +{movement}</span>
    } else if (movement < 0) {
      return <span className="text-red-600 text-xs">↘ {movement}</span>
    }
    return null
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Checking authentication...</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading props...</div>
      </div>
    )
  }

  const allVisibleProps = props.filter((p) => new Date(p.game_time).getTime() > nowMs)
  const visibleProps = allVisibleProps.slice(0, displayLimit)
  const hasMoreProps = allVisibleProps.length > displayLimit

  const loadMoreProps = () => {
    setDisplayLimit(prev => prev + 50)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">🎯 PrizePicks AI Cruncher</h1>
            <p className="text-gray-300">
              {sortBy === 'smart' ?
                `🌟 Best AI picks first • Showing ${visibleProps.length} of ${allVisibleProps.length} props` :
                `Real-time props with AI-powered analysis • ${visibleProps.length} active props`
              }
            </p>
            {user && (
              <div className="text-sm text-green-400 mt-1">
                ✓ Premium access active for {user.email}
              </div>
            )}
          </div>
          <div className="flex items-center space-x-4 mt-4 sm:mt-0">
            {lastUpdate && (
              <span className="text-sm text-gray-400">
                Updated {formatDistanceToNow(lastUpdate, { addSuffix: true })}
              </span>
            )}
            <button
              onClick={handleRefresh}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              Refresh Data
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
          <div className="flex flex-wrap items-center gap-4">
            <div>
              <label className="text-white text-sm font-medium mr-2">Sport:</label>
              <select
                value={selectedSport}
                onChange={(e) => setSelectedSport(e.target.value)}
                className="bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Sports</option>
                <option value="NFL">🏈 NFL</option>
                <option value="NCAAF">🏈 NCAAF</option>
                <option value="NBA">🏀 NBA</option>
                <option value="WNBA">🏀 WNBA</option>
                <option value="MLB">⚾ MLB</option>
                <option value="Tennis">🎾 Tennis</option>
              </select>
            </div>

            <div>
              <label className="text-white text-sm font-medium mr-2">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white"
              >
                <option value="smart">🎯 Best Picks First (AI + Time)</option>
                <option value="confidence">AI Rating Only</option>
                <option value="game_time">Game Time Only</option>
                <option value="movement">Line Movement</option>
              </select>
            </div>

            <button
              onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
              className="text-white hover:text-gray-300 transition-colors"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>



        {/* Props Table */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/20">
                <tr>
                  <th className="px-6 py-4 text-left text-white font-medium">Player</th>
                  <th className="px-6 py-4 text-left text-white font-medium">Prop</th>
                  <th className="px-6 py-4 text-left text-white font-medium">Line</th>
                  <th className="px-6 py-4 text-left text-white font-medium">Movement</th>
                  <th className="px-6 py-4 text-left text-white font-medium">
                    AI Rating {sortBy === 'smart' && <span className="text-yellow-400">⭐</span>}
                  </th>
                  <th className="px-6 py-4 text-left text-white font-medium">Recommendation</th>
                  <th className="px-6 py-4 text-left text-white font-medium">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {visibleProps.map((prop) => (
                  <tr key={prop.id} className="hover:bg-white/5 transition-colors">
                    <td className="px-6 py-4">
                      <div className="text-white font-medium">{prop.player_name}</div>
                      <div className="text-gray-400 text-sm">
                        {SPORT_EMOJIS[prop.sport as keyof typeof SPORT_EMOJIS]} {prop.team}
                        {prop.opponent && ` vs ${prop.opponent}`}
                      </div>
                      <div className="text-gray-500 text-xs mt-1">
                        {formatGameTime(prop.game_time)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-white">
                        {PROP_TYPE_LABELS[prop.prop_type as keyof typeof PROP_TYPE_LABELS] || prop.prop_type}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-white font-mono text-lg">{prop.line_value}</div>
                      {getMovementIndicator(prop)}
                    </td>
                    <td className="px-6 py-4">
                      {prop.movement_count ? (
                        <div className="text-yellow-400 text-sm">
                          {prop.movement_count} changes
                        </div>
                      ) : (
                        <div className="text-gray-500 text-sm">Stable</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      {prop.ai_analysis ? (
                        <div className="text-lg">
                          {getConfidenceDisplay(prop.ai_analysis.confidence_rating)}
                        </div>
                      ) : (
                        <div className="text-gray-500 text-sm">No analysis</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      {prop.ai_analysis ? (
                        <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getRecommendationStyle(prop.ai_analysis.recommendation)}`}>
                          {prop.ai_analysis.recommendation}
                        </span>
                      ) : (
                        <span className="text-gray-500 text-sm">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      {prop.ai_analysis ? (
                        <button
                          onClick={() => handleBreakdownClick(prop)}
                          disabled={breakdownLoading}
                          className="bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        >
                          {breakdownLoading ? 'Loading...' : 'BREAKDOWN'}
                        </button>
                      ) : (
                        <button
                          onClick={() => analyzeProps(prop.id)}
                          disabled={analyzingProps.has(prop.id)}
                          className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        >
                          {analyzingProps.has(prop.id) ? 'Analyzing...' : 'Analyze'}
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Load More Button */}
        {hasMoreProps && (
          <div className="text-center py-6">
            <button
              onClick={loadMoreProps}
              className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Load More Props ({allVisibleProps.length - displayLimit} remaining)
            </button>
            <div className="text-gray-400 text-sm mt-2">
              Showing {visibleProps.length} of {allVisibleProps.length} total props
            </div>
          </div>
        )}

        {visibleProps.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-xl mb-4">No active props found</div>
            <button
              onClick={handleRefresh}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium"
            >
              Refresh Data
            </button>
          </div>
        )}
      </div>
      {/* Breakdown Modal */}
      {breakdownProp && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4" onClick={() => setBreakdownProp(null)}>
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full overflow-hidden" onClick={(e) => e.stopPropagation()}>
            <div className="px-6 py-4 border-b">
              <div className="text-xl font-semibold">Bet Details: Breakdown</div>
              <div className="text-gray-600 text-sm mt-1">
                {breakdownProp.player_name} • {breakdownProp.prop_type.replace(/_/g, ' ')} • Line: {breakdownProp.line_value}
              </div>
            </div>
            <div className="p-6 space-y-4">
              {breakdownLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-3 text-gray-600">Generating enhanced breakdown...</span>
                </div>
              ) : breakdownData?.breakdown_sections ? (
                <>
                  {/* Executive Summary */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-blue-900 mb-2">Executive Summary</h3>
                    <p className="text-blue-800 text-sm">{breakdownData.breakdown_sections.executive_summary}</p>
                  </div>

                  {/* Recommendation with Confidence */}
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Recommendation:</span>{' '}
                      <span className="font-bold text-lg">
                        {breakdownProp.ai_analysis?.recommendation}
                      </span>
                    </div>
                    <div className="text-yellow-600">
                      {Array.from({ length: breakdownProp.ai_analysis?.confidence_rating || 3 }).map((_, i) => '🔥')}
                      <span className="ml-2 text-sm text-gray-600">
                        {breakdownProp.ai_analysis?.confidence_rating}/5
                      </span>
                    </div>
                  </div>

                  {/* Key Factors */}
                  <div>
                    <h3 className="font-semibold mb-2">Key Factors</h3>
                    <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                      {breakdownData.breakdown_sections.key_factors.map((factor: string, idx: number) => (
                        <li key={idx}>{factor}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Pros and Cons */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-green-900 mb-2">Pros</h3>
                      <ul className="list-disc list-inside text-sm text-green-800 space-y-1">
                        {breakdownData.breakdown_sections.pros.map((pro: string, idx: number) => (
                          <li key={idx}>{pro}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-red-900 mb-2">Cons</h3>
                      <ul className="list-disc list-inside text-sm text-red-800 space-y-1">
                        {breakdownData.breakdown_sections.cons.map((con: string, idx: number) => (
                          <li key={idx}>{con}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Risk Assessment */}
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-yellow-900 mb-2">Risk Assessment</h3>
                    <p className="text-yellow-800 text-sm">{breakdownData.breakdown_sections.risk_assessment}</p>
                  </div>

                  {/* Final Verdict */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 mb-2">Final Verdict</h3>
                    <p className="text-gray-800 text-sm font-medium">{breakdownData.breakdown_sections.final_verdict}</p>
                  </div>
                </>
              ) : breakdownProp.ai_analysis ? (
                <>
                  {/* Fallback to basic analysis */}
                  <div>
                    <span className="text-sm font-medium text-gray-600">Recommendation:</span>{' '}
                    <span className="font-semibold">
                      {breakdownProp.ai_analysis.recommendation}
                    </span>
                    <span className="ml-2 text-yellow-600">
                      {Array.from({ length: breakdownProp.ai_analysis.confidence_rating || 3 }).map((_, i) => '🔥')}
                    </span>
                  </div>

                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {breakdownProp.ai_analysis.analysis_text}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <div className="font-semibold mb-2">Key Factors</div>
                      <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                        {(breakdownProp.ai_analysis.reasoning_points || []).slice(0,3).map((p, idx) => (
                          <li key={idx}>{p}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <div className="font-semibold mb-2">2 counterpoints</div>
                      <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                        <li>Market/line movement risk leading up to game time</li>
                        <li>Outcome variance — props can miss despite strong context</li>
                      </ul>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-gray-600">No analysis available yet.</div>
              )}
            </div>
            <div className="px-6 py-4 border-t flex justify-between items-center">
              <div className="text-xs text-gray-500">
                {breakdownData?.cost_estimate && `Cost: $${breakdownData.cost_estimate.toFixed(4)}`}
              </div>
              <button
                onClick={() => {
                  setBreakdownProp(null)
                  setBreakdownData(null)
                }}
                className="bg-slate-900 text-white px-4 py-2 rounded-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

    </div>
  )
}