'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

const supabase = createSupabaseClient()

export default function PrizePicksInput() {
  const [user, setUser] = useState<any>(null)
  const [authLoading, setAuthLoading] = useState(true)
  const [rawInput, setRawInput] = useState('')
  const [selectedDate, setSelectedDate] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState('')
  const [showPasswordPrompt, setShowPasswordPrompt] = useState(false)
  const [password, setPassword] = useState('')
  const [hasAccess, setHasAccess] = useState(false)
  const [analyzeLoading, setAnalyzeLoading] = useState(false)
  const [analyzeResult, setAnalyzeResult] = useState<any>(null)
  const [cleanupLoading, setCleanupLoading] = useState(false)
  const [cleanupResult, setCleanupResult] = useState<any>(null)
  const [sportFixLoading, setSportFixLoading] = useState(false)
  const [sportFixResult, setSportFixResult] = useState<any>(null)
  const [analysisStats, setAnalysisStats] = useState<any>(null)
  const [analysisStatsLoading, setAnalysisStatsLoading] = useState(false)
  const [analysisMode, setAnalysisMode] = useState<'unanalyzed' | 'all'>('unanalyzed')
  const [autoImportLoading, setAutoImportLoading] = useState(false)
  const [autoImportResult, setAutoImportResult] = useState<any>(null)

  // Check authentication and access
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        window.location.href = `/login?next=${encodeURIComponent('/prizepicks/input')}`
        return
      }

      // Check if user has authorized email
      const authorizedEmails = ['<EMAIL>', '<EMAIL>']
      if (!authorizedEmails.includes(user.email || '')) {
        setError('This feature is only available for authorized users')
        setAuthLoading(false)
        return
      }

      setUser(user)
      setAuthLoading(false)
      setShowPasswordPrompt(true)

      // Set default date to today
      setSelectedDate(new Date().toISOString().split('T')[0])
    }

    checkAuth()
  }, [])

  const handlePasswordSubmit = async () => {
    try {
      const response = await fetch('/api/prizepicks/verify-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      })

      if (response.ok) {
        setHasAccess(true)
        setShowPasswordPrompt(false)
        setPassword('')
      } else {
        setError('Incorrect password')
      }
    } catch (error) {
      setError('Access verification failed')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!rawInput.trim()) {
      setError('Please paste PrizePicks projections')
      return
    }

    setLoading(true)
    setError('')
    setResult(null)

    try {
      const response = await fetch('/api/prizepicks/manual-input', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          rawInput: rawInput.trim(),
          date: selectedDate
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process input')
      }

      setResult(data)
      setRawInput('')

    } catch (error) {
      console.error('Input error:', error)
      setError(error instanceof Error ? error.message : 'Failed to process input')
    } finally {
      setLoading(false)
    }
  }

  const loadAnalysisStats = async () => {
    setAnalysisStatsLoading(true)
    try {
      const res = await fetch('/api/prizepicks/analysis-stats')
      const data = await res.json()
      if (res.ok) {
        setAnalysisStats(data)
      }
    } catch (e) {
      console.error('Failed to load analysis stats:', e)
    } finally {
      setAnalysisStatsLoading(false)
    }
  }

  const handleRunAnalyses = async () => {
    setAnalyzeLoading(true)
    setAnalyzeResult(null)
    setError('')
    try {
      console.log(`🚀 Starting ${analysisMode} batch analysis v2...`)
      const res = await fetch('/api/prizepicks/batch-analyze-v2', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          maxAnalyses: 200,
          forceRefresh: analysisMode === 'all',
          unanalyzedOnly: analysisMode === 'unanalyzed'
        })
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Batch analysis failed')

      console.log('✅ Batch analysis complete:', data.result)
      setAnalyzeResult(data.result || data)

      // Refresh stats after analysis
      await loadAnalysisStats()
    } catch (e) {
      console.error('❌ Batch analysis failed:', e)
      setError(e instanceof Error ? e.message : 'Failed to run analyses')
    } finally {
      setAnalyzeLoading(false)
    }
  }

  const handleAutoImport = async () => {
    setAutoImportLoading(true)
    setAutoImportResult(null)
    setError('')
    try {
      console.log('🚀 Starting auto-import from PrizePicks API...')
      const res = await fetch('/api/prizepicks/auto-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          leagues: ['NFL', 'WNBA', 'NCAAF', 'MLB']
        })
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Auto-import failed')

      console.log('✅ Auto-import complete:', data)
      setAutoImportResult(data)

      // Refresh stats after import
      await loadAnalysisStats()
    } catch (e) {
      console.error('❌ Auto-import failed:', e)
      setError(e instanceof Error ? e.message : 'Failed to auto-import')
    } finally {
      setAutoImportLoading(false)
    }
  }

  const handleCleanupOld = async () => {
    setCleanupLoading(true)
    setCleanupResult(null)
    setError('')
    try {
      console.log('🧹 Starting cleanup of old props...')
      const res = await fetch('/api/prizepicks/cleanup-old', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Cleanup failed')

      console.log('✅ Cleanup complete:', data)
      setCleanupResult(data)

      // Refresh stats after cleanup
      await loadAnalysisStats()
    } catch (e) {
      console.error('❌ Cleanup failed:', e)
      setError(e instanceof Error ? e.message : 'Failed to cleanup')
    } finally {
      setCleanupLoading(false)
    }
  }

  // Load analysis stats on component mount
  useEffect(() => {
    if (hasAccess) {
      loadAnalysisStats()
    }
  }, [hasAccess])

  const handleCleanupMalformed = async () => {
    setCleanupLoading(true)
    setCleanupResult(null)
    setError('')
    try {
      console.log('🧹 Starting cleanup of malformed entries...')
      const res = await fetch('/api/prizepicks/cleanup-malformed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Cleanup failed')

      console.log('✅ Cleanup complete:', data)
      setCleanupResult(data)
    } catch (e) {
      console.error('❌ Cleanup failed:', e)
      setError(e instanceof Error ? e.message : 'Failed to cleanup malformed entries')
    } finally {
      setCleanupLoading(false)
    }
  }

  const handleFixSports = async () => {
    setSportFixLoading(true)
    setSportFixResult(null)
    setError('')
    try {
      console.log('🔧 Starting sport classification fix...')
      const res = await fetch('/api/prizepicks/fix-sports', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Sport fix failed')

      console.log('✅ Sport fix complete:', data)
      setSportFixResult(data)
    } catch (e) {
      console.error('❌ Sport fix failed:', e)
      setError(e instanceof Error ? e.message : 'Failed to fix sport classifications')
    } finally {
      setSportFixLoading(false)
    }
  }

  const exampleInput = `Lamar Jackson
BAL - QB
Lamar Jackson
vs DET Mon 8:15pm
233.5
Pass Yards
Less
More
Trending
37.0K
Derrick Henry
BAL - RB
Derrick Henry
vs DET Mon 8:15pm
86.5
Rush Yards
Less
More
Trending
27.8K`

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Checking authentication...</div>
      </div>
    )
  }

  if (error && !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="bg-red-500/20 border border-red-500 rounded-xl p-6 max-w-md">
          <div className="text-red-400 text-xl font-bold mb-2">Access Denied</div>
          <div className="text-red-300">{error}</div>
        </div>
      </div>
    )
  }

  if (showPasswordPrompt && !hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl p-6 max-w-md">
          <div className="text-white text-xl font-bold mb-4">🎯 PrizePicks AI Access</div>
          <div className="text-gray-300 mb-4">Enter the password to access the manual input system</div>
          <div className="space-y-4">
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              className="w-full bg-white/20 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-gray-400"
              onKeyPress={(e) => e.key === 'Enter' && handlePasswordSubmit()}
            />
            <button
              onClick={handlePasswordSubmit}
              disabled={!password.trim()}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-3 rounded-lg font-medium transition-colors"
            >
              Access System
            </button>
            {error && (
              <div className="text-red-400 text-sm">{error}</div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">📝 PrizePicks Manual Input</h1>
          <p className="text-gray-300">
            Daily input for PrizePicks projections • Recommended time: 12:00 PM EST
          </p>
          {user && (
            <div className="text-sm text-green-400 mt-1">
              ✓ Admin access: {user.email}
            </div>
          )}
        </div>

        {/* Input Form */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Date Selection */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Date (for props being input)
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="bg-white/20 border border-white/30 rounded-lg px-4 py-2 text-white w-full max-w-xs"
              />
            </div>

            {/* Raw Input Textarea */}
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                PrizePicks Projections (paste format)
              </label>
              <textarea
                value={rawInput}
                onChange={(e) => setRawInput(e.target.value)}
                placeholder={`Paste your PrizePicks projections here, one per line:

${exampleInput}`}
                className="w-full h-64 bg-white/20 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-gray-400 font-mono text-sm"
                disabled={loading}
              />
              <div className="text-gray-400 text-xs mt-2">
                Paste the raw PrizePicks mobile app format (copy all props and paste directly)
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <button
                  type="submit"
                  disabled={loading || !rawInput.trim()}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {loading ? 'Processing...' : 'Import Projections'}
                </button>

                {/* Analysis Mode Selection */}
                <div className="flex items-center gap-2">
                  <select
                    value={analysisMode}
                    onChange={(e) => setAnalysisMode(e.target.value as 'unanalyzed' | 'all')}
                    className="bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white text-sm"
                  >
                    <option value="unanalyzed" className="bg-gray-800">Unanalyzed Only</option>
                    <option value="all" className="bg-gray-800">All Props (Force Refresh)</option>
                  </select>

                  <button
                    type="button"
                    onClick={handleRunAnalyses}
                    disabled={analyzeLoading}
                    className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors relative"
                  >
                    {analyzeLoading ? 'Running…' : (
                      <div className="flex flex-col items-center">
                        <span>Run AI Analyses</span>
                        {analysisStats && (
                          <span className="text-xs text-purple-200">
                            {analysisMode === 'unanalyzed'
                              ? `${analysisStats.pending_analysis} unanalyzed`
                              : `${analysisStats.total_props} total props`
                            }
                          </span>
                        )}
                      </div>
                    )}
                  </button>
                </div>

                <button
                  type="button"
                  onClick={handleCleanupMalformed}
                  disabled={cleanupLoading}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {cleanupLoading ? 'Cleaning...' : 'Cleanup Malformed'}
                </button>

                <button
                  type="button"
                  onClick={handleFixSports}
                  disabled={sportFixLoading}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {sportFixLoading ? 'Fixing...' : 'Fix Sports Icons'}
                </button>

                <button
                  type="button"
                  onClick={handleAutoImport}
                  disabled={autoImportLoading}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {autoImportLoading ? 'Importing...' : '🚀 Auto Import from API'}
                </button>

                <button
                  type="button"
                  onClick={handleCleanupOld}
                  disabled={cleanupLoading}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  {cleanupLoading ? 'Cleaning...' : '🧹 Clean Old Props'}
                </button>
              </div>

              {selectedDate && (
                <div className="text-gray-300 text-sm">
                  Will import for: {new Date(selectedDate + 'T00:00:00').toLocaleDateString('en-US', {
                    month: 'numeric',
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Analysis Stats Display */}
        {analysisStats && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white text-lg font-semibold">📊 Analysis Status</h3>
              <button
                onClick={loadAnalysisStats}
                disabled={analysisStatsLoading}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                {analysisStatsLoading ? 'Loading...' : 'Refresh'}
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-500/20 border border-blue-500 rounded-lg p-4">
                <div className="text-blue-300 text-sm font-medium">Total Props</div>
                <div className="text-blue-100 text-2xl font-bold">{analysisStats.total_props || 0}</div>
              </div>

              <div className="bg-green-500/20 border border-green-500 rounded-lg p-4">
                <div className="text-green-300 text-sm font-medium">Analyzed</div>
                <div className="text-green-100 text-2xl font-bold">{analysisStats.analyzed_props || 0}</div>
              </div>

              <div className="bg-orange-500/20 border border-orange-500 rounded-lg p-4">
                <div className="text-orange-300 text-sm font-medium">Pending Analysis</div>
                <div className="text-orange-100 text-2xl font-bold">{analysisStats.pending_analysis || 0}</div>
              </div>
            </div>

            <div className="mt-4 bg-gray-700/50 rounded-lg p-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-300">Completion Rate</span>
                <span className="text-white font-medium">{analysisStats.completion_percentage || 0}%</span>
              </div>
              <div className="mt-2 bg-gray-600 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(analysisStats.completion_percentage || 0, 100)}%` }}
                />
              </div>
            </div>

            {analysisStats.ready_for_betting && (
              <div className="mt-3 bg-green-500/20 border border-green-500 rounded-lg p-3">
                <div className="text-green-300 text-sm font-medium">✅ Ready for betting (80%+ analyzed)</div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-500/20 border border-red-500 rounded-xl p-4 mb-6">
            <div className="text-red-400 font-medium">Error</div>
            <div className="text-red-300 text-sm mt-1">{error}</div>
          </div>
        )}

        {/* Success Result */}
        {result && (
          <div className="bg-green-500/20 border border-green-500 rounded-xl p-6 mb-6">
            <div className="text-green-400 font-bold text-lg mb-2">✅ Import Successful</div>
            <div className="text-green-300 space-y-1">
              <div>• Processed {result.parsed} props from {result.raw_lines} lines</div>
              <div>• Imported {result.props} props to database</div>
              <div>• Date: {result.date}</div>
              <div>• AI analysis triggered automatically</div>
            </div>
        {/* Batch Analysis Result */}
        {analyzeResult && (
          <div className="bg-purple-500/20 border border-purple-500 rounded-xl p-6 mb-6">
            <div className="text-purple-300 font-bold mb-2">🧠 AI Analyses Complete (v2)</div>
            <div className="text-purple-200 text-sm space-y-1">
              <div>Analyzed: {analyzeResult.analyzed ?? 0}</div>
              <div>Cached/Skipped: {analyzeResult.cached ?? 0}</div>
              <div>Failed: {analyzeResult.failed ?? 0}</div>
              <div>Total Cost: ${(analyzeResult.total_cost ?? 0).toFixed(3)}</div>
              <div>Claude Cost: ${(analyzeResult.claude_cost ?? 0).toFixed(3)}</div>
              <div>DeepSeek Cost: ${(analyzeResult.deepseek_cost ?? 0).toFixed(3)}</div>
              <div>Avg per Prop: ${(analyzeResult.average_cost_per_prop ?? 0).toFixed(4)}</div>
              <div>Processing Time: {((analyzeResult.processing_time_ms ?? 0) / 1000).toFixed(1)}s</div>
            </div>
          </div>
        )}

        {/* Cleanup Results */}
        {cleanupResult && (
          <div className="bg-red-500/20 border border-red-500 rounded-xl p-6 mb-6">
            <div className="text-red-300 font-bold mb-2">🧹 Cleanup Complete</div>
            <div className="text-red-200 text-sm space-y-1">
              <div>Cleaned: {cleanupResult.cleaned || 0} malformed entries</div>
              <div>Reviewed: {cleanupResult.candidates_reviewed || 0} total entries</div>
              <div>{cleanupResult.message}</div>
              {cleanupResult.deleted_entries && cleanupResult.deleted_entries.length > 0 && (
                <div className="mt-2">
                  <div className="font-medium">Deleted entries:</div>
                  <div className="text-xs space-y-1 ml-2">
                    {cleanupResult.deleted_entries.slice(0, 5).map((entry: any, i: number) => (
                      <div key={i}>• "{entry.player_name}" ({entry.team}, {entry.sport})</div>
                    ))}
                    {cleanupResult.deleted_entries.length > 5 && (
                      <div>• ...and {cleanupResult.deleted_entries.length - 5} more</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Sport Fix Results */}
        {sportFixResult && (
          <div className="bg-blue-500/20 border border-blue-500 rounded-xl p-6 mb-6">
            <div className="text-blue-300 font-bold mb-2">🔧 Sport Classification Fix Complete</div>
            <div className="text-blue-200 text-sm space-y-1">
              <div>Fixed: {sportFixResult.fixed || 0} sport classifications</div>
              <div>Reviewed: {sportFixResult.total_reviewed || 0} total props</div>
              <div>{sportFixResult.message}</div>
              {sportFixResult.updates && sportFixResult.updates.length > 0 && (
                <div className="mt-2">
                  <div className="font-medium">Updated entries:</div>
                  <div className="text-xs space-y-1 ml-2">
                    {sportFixResult.updates.slice(0, 5).map((update: any, i: number) => (
                      <div key={i}>• {update.player_name} ({update.team}): {update.old_sport} → {update.new_sport}</div>
                    ))}
                    {sportFixResult.updates.length > 5 && (
                      <div>• ...and {sportFixResult.updates.length - 5} more</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Cleanup Results */}
        {cleanupResult && (
          <div className="bg-red-500/20 border border-red-500 rounded-xl p-6 mb-6">
            <div className="text-red-300 font-bold mb-2">🧹 Cleanup Complete</div>
            <div className="text-red-200 text-sm space-y-1">
              <div>Cleaned: {cleanupResult.cleaned || 0} old props</div>
              <div>Reviewed: {cleanupResult.total_reviewed || 0} total props</div>
              <div>{cleanupResult.message}</div>
              {cleanupResult.deleted_props && cleanupResult.deleted_props.length > 0 && (
                <div className="mt-2">
                  <div className="font-medium">Sample deleted props:</div>
                  <div className="text-xs space-y-1 ml-2">
                    {cleanupResult.deleted_props.slice(0, 3).map((prop: any, i: number) => (
                      <div key={i}>• {prop.player_name} ({prop.team}, {prop.sport})</div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Auto Import Results */}
        {autoImportResult && (
          <div className="bg-green-500/20 border border-green-500 rounded-xl p-6 mb-6">
            <div className="text-green-300 font-bold mb-2">🚀 Auto Import Complete</div>
            <div className="text-green-200 text-sm space-y-1">
              <div>Imported: {autoImportResult.imported || 0} new props</div>
              <div>Updated: {autoImportResult.updated || 0} existing props</div>
              <div>Errors: {autoImportResult.errors || 0}</div>
              <div>Total Processed: {autoImportResult.total_processed || 0}</div>
              <div>Leagues: {autoImportResult.leagues?.join(', ') || 'N/A'}</div>
              <div className="text-xs text-green-300 mt-2">
                Source: PrizePicks API • {autoImportResult.metadata?.scraped_at}
              </div>
            </div>
          </div>
        )}

            <div className="mt-4">
              <a
                href="/prizepicks/cruncher"
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors inline-block"
              >
                View in Cruncher →
              </a>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6">
          <h3 className="text-white font-bold text-lg mb-4">📋 How to Use</h3>
          <div className="text-gray-300 space-y-3 text-sm">
            <div className="flex items-start space-x-2">
              <span className="text-blue-400 font-bold">1.</span>
              <span>Go to PrizePicks and copy your target projections</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-400 font-bold">2.</span>
              <span>Paste the raw multi-line format directly from the PrizePicks mobile app</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-400 font-bold">3.</span>
              <span>Select the correct date (usually today or tomorrow)</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-400 font-bold">4.</span>
              <span>Click "Import Projections" - AI analysis will run automatically</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-blue-400 font-bold">5.</span>
              <span>View results in the Cruncher with AI recommendations and sharp line comparisons</span>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-500/20 border border-yellow-500 rounded-lg">
            <div className="text-yellow-400 font-medium mb-2">⏰ Recommended Schedule</div>
            <div className="text-yellow-300 text-sm">
              Import daily at 12:00 PM EST for best results. The system will pull sharp lines from Pinnacle,
              Circa, and DraftKings for comparison, then provide AI-powered analysis and recommendations.
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}