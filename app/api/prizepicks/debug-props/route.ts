// Debug API to check what props are in the database
import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()

    console.log('🔍 Debugging PrizePicks props...')

    // Get total count
    const { count, error: countError } = await supabase
      .from('prizepicks_props')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      console.error('Count error:', countError)
      return NextResponse.json({ error: 'Count failed', details: countError.message }, { status: 500 })
    }

    console.log(`📊 Total props: ${count}`)

    if (count === 0) {
      return NextResponse.json({
        total: 0,
        message: 'No props found in database',
        debug: 'Database appears to be empty'
      })
    }

    // Get sample data
    const { data: props, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, prop_type, is_active, created_at')
      .order('created_at', { ascending: false })
      .limit(20)

    if (fetchError) {
      console.error('Fetch error:', fetchError)
      return NextResponse.json({ error: 'Fetch failed', details: fetchError.message }, { status: 500 })
    }

    // Count by sport
    const { data: allProps, error: allError } = await supabase
      .from('prizepicks_props')
      .select('sport, prop_type, team')

    let sportCounts = {}
    let propTypeCounts = {}
    let mlbPropsInNFL = []

    if (!allError && allProps) {
      allProps.forEach(prop => {
        // Count by sport
        sportCounts[prop.sport] = (sportCounts[prop.sport] || 0) + 1
        
        // Count by prop type
        propTypeCounts[prop.prop_type] = (propTypeCounts[prop.prop_type] || 0) + 1
        
        // Check for MLB props marked as NFL
        const mlbPropTypes = ['hits', 'rbis', 'runs', 'strikeouts', 'total_bases', 'runs_batted_in', 'runs_scored', 'home_runs']
        const mlbTeams = ['DET', 'CLE', 'BAL', 'BOS', 'NYY', 'TB', 'TOR', 'MIN', 'KC', 'HOU', 'LAA', 'OAK', 'SEA', 'TEX', 'ARI', 'ATL', 'CHC', 'CWS', 'CIN', 'COL', 'MIA', 'MIL', 'NYM', 'PHI', 'PIT', 'SD', 'SF', 'STL', 'WSH']
        
        if (prop.sport === 'NFL' && (mlbPropTypes.includes(prop.prop_type) || mlbTeams.includes(prop.team))) {
          mlbPropsInNFL.push({
            team: prop.team,
            prop_type: prop.prop_type,
            reason: mlbPropTypes.includes(prop.prop_type) ? 'MLB prop type' : 'MLB team'
          })
        }
      })
    }

    return NextResponse.json({
      total: count,
      sample_props: props?.map(p => ({
        player_name: p.player_name,
        team: p.team,
        sport: p.sport,
        prop_type: p.prop_type,
        is_active: p.is_active,
        created_at: p.created_at
      })),
      sport_counts: sportCounts,
      prop_type_counts: propTypeCounts,
      mlb_props_in_nfl: mlbPropsInNFL.slice(0, 10), // First 10 examples
      total_mlb_in_nfl: mlbPropsInNFL.length,
      debug: {
        count_query_worked: !countError,
        fetch_query_worked: !fetchError,
        all_props_query_worked: !allError
      }
    })

  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json({ 
      error: 'Debug failed', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
