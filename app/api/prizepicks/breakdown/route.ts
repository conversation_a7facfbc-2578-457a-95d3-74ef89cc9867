// Breakdown Popup API - Enhanced breakdown generation for individual props
// Uses existing analysis data + DeepSeek for detailed breakdown formatting

import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'
import OpenAI from 'openai'

const deepseek = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: process.env.DEEPSEEK_API_KEY
})

interface BreakdownResponse {
  prop_id: string
  player_name: string
  prop_type: string
  line_value: number
  recommendation: string
  confidence_rating: number
  breakdown_sections: {
    executive_summary: string
    key_factors: string[]
    pros: string[]
    cons: string[]
    risk_assessment: string
    final_verdict: string
  }
  cost_estimate: number
  generated_at: string
}

export async function POST(request: NextRequest) {
  try {
    const { propId } = await request.json()

    if (!propId) {
      return NextResponse.json({ error: 'Prop ID required' }, { status: 400 })
    }

    const supabase = createSupabaseServerClient()

    // Get prop details and existing analysis
    const { data: propData, error: propError } = await supabase
      .from('prizepicks_props')
      .select(`
        *,
        ai_analyses (
          analysis_text,
          confidence_rating,
          recommendation,
          reasoning_points,
          edge_summary,
          created_at
        )
      `)
      .eq('id', propId)
      .single()

    if (propError || !propData) {
      return NextResponse.json({ error: 'Prop not found' }, { status: 404 })
    }

    // Check if we have recent analysis
    const existingAnalysis = propData.ai_analyses?.[0]
    if (!existingAnalysis) {
      return NextResponse.json({ 
        error: 'No analysis available for this prop. Please run analysis first.' 
      }, { status: 404 })
    }

    // Check if analysis is recent (within 6 hours)
    const analysisAge = Date.now() - new Date(existingAnalysis.created_at).getTime()
    const hoursOld = analysisAge / (1000 * 60 * 60)
    
    if (hoursOld > 6) {
      return NextResponse.json({ 
        error: 'Analysis is stale. Please refresh analysis first.' 
      }, { status: 410 })
    }

    console.log(`🔍 Generating breakdown for ${propData.player_name} ${propData.prop_type}`)

    // Generate enhanced breakdown using DeepSeek
    const breakdown = await generateEnhancedBreakdown(propData, existingAnalysis)

    const response: BreakdownResponse = {
      prop_id: propId,
      player_name: propData.player_name,
      prop_type: propData.prop_type,
      line_value: propData.line_value,
      recommendation: existingAnalysis.recommendation,
      confidence_rating: existingAnalysis.confidence_rating,
      breakdown_sections: breakdown,
      cost_estimate: 0.003, // DeepSeek cost
      generated_at: new Date().toISOString()
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Breakdown generation error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate breakdown',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}

async function generateEnhancedBreakdown(propData: any, analysis: any) {
  const prompt = `Create a detailed breakdown for this sports betting analysis popup.

PROP DETAILS:
- Player: ${propData.player_name}
- Team: ${propData.team}${propData.opponent ? ` vs ${propData.opponent}` : ''}
- Bet: ${propData.prop_type} ${propData.line_value}
- Game: ${new Date(propData.game_time).toLocaleDateString()}

EXISTING ANALYSIS:
- Recommendation: ${analysis.recommendation}
- Confidence: ${analysis.confidence_rating}/5
- Analysis: ${analysis.analysis_text}
- Key Points: ${analysis.reasoning_points?.join(', ') || 'None'}
- Edge Summary: ${analysis.edge_summary || 'No edge assessment'}

Create a structured breakdown for a popup modal in JSON format:
{
  "executive_summary": "2-3 sentence summary of the bet and recommendation",
  "key_factors": [
    "Most important factor affecting this bet",
    "Second most important factor",
    "Third most important factor"
  ],
  "pros": [
    "Reason supporting the recommendation",
    "Another supporting reason",
    "Third supporting reason"
  ],
  "cons": [
    "Risk factor or opposing argument",
    "Another risk or concern",
    "Third potential issue"
  ],
  "risk_assessment": "1-2 sentence assessment of the main risks and variance",
  "final_verdict": "Clear, confident final recommendation with reasoning (2-3 sentences)"
}`

  try {
    const response = await deepseek.chat.completions.create({
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      max_tokens: 800,
      response_format: { type: 'json_object' }
    })

    const content = response.choices[0]?.message?.content || '{}'
    const breakdown = JSON.parse(content)
    
    // Validate and provide fallbacks
    return {
      executive_summary: breakdown.executive_summary || `${analysis.recommendation} recommendation for ${propData.player_name} ${propData.prop_type} ${propData.line_value}`,
      key_factors: breakdown.key_factors || analysis.reasoning_points || ['Analysis data unavailable'],
      pros: breakdown.pros || ['Supporting analysis available in main breakdown'],
      cons: breakdown.cons || ['Risk factors identified in main analysis'],
      risk_assessment: breakdown.risk_assessment || 'Standard betting risks apply',
      final_verdict: breakdown.final_verdict || `${analysis.recommendation} with ${analysis.confidence_rating}/5 confidence`
    }
    
  } catch (error) {
    console.error('DeepSeek breakdown error:', error)
    
    // Fallback breakdown using existing analysis
    return {
      executive_summary: `${analysis.recommendation} recommendation for ${propData.player_name} ${propData.prop_type} ${propData.line_value} with ${analysis.confidence_rating}/5 confidence.`,
      key_factors: analysis.reasoning_points || ['Analysis available in main view'],
      pros: [`Recommendation: ${analysis.recommendation}`, 'Based on comprehensive analysis'],
      cons: ['Standard betting risks apply', 'Market conditions may change'],
      risk_assessment: 'Standard variance and risk factors apply to this bet.',
      final_verdict: `${analysis.recommendation} with ${analysis.confidence_rating}/5 confidence based on available data.`
    }
  }
}

// Get breakdown for multiple props (batch)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const propIds = searchParams.get('propIds')?.split(',') || []

    if (propIds.length === 0) {
      return NextResponse.json({ error: 'No prop IDs provided' }, { status: 400 })
    }

    if (propIds.length > 10) {
      return NextResponse.json({ error: 'Maximum 10 props per request' }, { status: 400 })
    }

    const supabase = createSupabaseServerClient()

    // Get props with analysis
    const { data: propsData, error: propsError } = await supabase
      .from('prizepicks_props')
      .select(`
        id,
        player_name,
        prop_type,
        line_value,
        ai_analyses (
          recommendation,
          confidence_rating,
          created_at
        )
      `)
      .in('id', propIds)

    if (propsError) {
      return NextResponse.json({ error: 'Failed to fetch props' }, { status: 500 })
    }

    const summaries = propsData?.map(prop => {
      const analysis = prop.ai_analyses?.[0]
      const analysisAge = analysis ? 
        (Date.now() - new Date(analysis.created_at).getTime()) / (1000 * 60 * 60) : 
        999

      return {
        prop_id: prop.id,
        player_name: prop.player_name,
        prop_type: prop.prop_type,
        line_value: prop.line_value,
        recommendation: analysis?.recommendation || 'NO_ANALYSIS',
        confidence_rating: analysis?.confidence_rating || 0,
        has_recent_analysis: analysisAge < 6,
        analysis_age_hours: Math.round(analysisAge * 10) / 10
      }
    }) || []

    return NextResponse.json({
      summaries,
      total_props: summaries.length,
      with_analysis: summaries.filter(s => s.has_recent_analysis).length,
      without_analysis: summaries.filter(s => !s.has_recent_analysis).length
    })

  } catch (error) {
    console.error('Batch breakdown error:', error)
    return NextResponse.json({ error: 'Batch breakdown failed' }, { status: 500 })
  }
}
