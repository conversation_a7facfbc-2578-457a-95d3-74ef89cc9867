import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET() {
  try {
    const supabase = createSupabaseServerClient()
    
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    // Get total active props for today and tomorrow
    const { count: totalProps } = await supabase
      .from('prizepicks_props')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('game_time', today.toISOString())
      .lt('game_time', tomorrow.toISOString())

    // Get analyzed props (within 6 hours)
    const { count: analyzedProps } = await supabase
      .from('ai_analyses')
      .select(`
        prop_id,
        prizepicks_props!inner(game_time)
      `, { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString())
      .gte('prizepicks_props.game_time', today.toISOString())
      .lt('prizepicks_props.game_time', tomorrow.toISOString())

    const pendingAnalysis = (totalProps || 0) - (analyzedProps || 0)
    const analysisCompletePercentage = totalProps ? (analyzedProps / totalProps) * 100 : 0

    return NextResponse.json({
      total_props: totalProps || 0,
      analyzed_props: analyzedProps || 0,
      pending_analysis: Math.max(pendingAnalysis, 0),
      completion_percentage: Math.round(analysisCompletePercentage * 10) / 10,
      ready_for_betting: analysisCompletePercentage >= 80,
      last_updated: new Date().toISOString()
    })

  } catch (error) {
    console.error('Analysis stats error:', error)
    return NextResponse.json({ error: 'Failed to fetch analysis stats' }, { status: 500 })
  }
}
