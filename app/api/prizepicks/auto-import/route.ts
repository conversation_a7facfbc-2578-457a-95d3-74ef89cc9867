import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/client'
import { scrapePrizePicksData } from '@/lib/prizepicks/api-scraper'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has authorized email
    const authorizedEmails = ['<EMAIL>', '<EMAIL>']
    if (!authorizedEmails.includes(user.email || '')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const { leagues } = await request.json()
    const targetLeagues = leagues || ['NFL', 'WNBA', 'NCAAF', 'MLB']

    console.log(`🚀 Starting auto-import for leagues: ${targetLeagues.join(', ')}`)

    // Scrape data from PrizePicks API
    const scrapedData = await scrapePrizePicksData(targetLeagues)
    const { props, metadata } = scrapedData

    if (props.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No props found for specified leagues',
        imported: 0,
        total_available: 0,
        metadata
      })
    }

    // Deactivate old props for these leagues
    console.log(`🧹 Deactivating old props for leagues: ${targetLeagues.join(', ')}`)
    const { error: deactivateError } = await supabase
      .from('prizepicks_props')
      .update({ is_active: false })
      .in('sport', targetLeagues)
      .eq('is_active', true)

    if (deactivateError) {
      console.error('Failed to deactivate old props:', deactivateError)
    }

    // Insert new props with upsert logic
    let imported = 0
    let updated = 0
    let errors = 0

    console.log(`📥 Importing ${props.length} props...`)

    for (const prop of props) {
      try {
        // Check if prop already exists
        const { data: existingProp } = await supabase
          .from('prizepicks_props')
          .select('id, line_value')
          .eq('external_id', prop.external_id)
          .single()

        if (existingProp) {
          // Update existing prop if line changed
          if (existingProp.line_value !== prop.line_value) {
            const { error: updateError } = await supabase
              .from('prizepicks_props')
              .update({
                line_value: prop.line_value,
                game_time: prop.game_time,
                is_active: true,
                scraped_at: prop.scraped_at
              })
              .eq('id', existingProp.id)

            if (updateError) {
              console.error(`Failed to update prop ${prop.external_id}:`, updateError)
              errors++
            } else {
              updated++
            }
          }
        } else {
          // Insert new prop
          const { error: insertError } = await supabase
            .from('prizepicks_props')
            .insert([{
              external_id: prop.external_id,
              sport: prop.sport,
              player_name: prop.player_name,
              team: prop.team,
              opponent: prop.opponent,
              prop_type: prop.prop_type,
              line_value: prop.line_value,
              game_time: prop.game_time,
              is_active: prop.is_active,
              scraped_at: prop.scraped_at
            }])

          if (insertError) {
            console.error(`Failed to insert prop ${prop.external_id}:`, insertError)
            errors++
          } else {
            imported++
          }
        }
      } catch (propError) {
        console.error(`Error processing prop ${prop.external_id}:`, propError)
        errors++
      }
    }

    // Log line movements for props that changed
    if (updated > 0) {
      console.log(`📈 Detected ${updated} line movements`)
      // You could insert into line_movements table here if you want to track changes
    }

    const result = {
      success: true,
      message: `Auto-import completed successfully`,
      imported,
      updated,
      errors,
      total_processed: props.length,
      leagues: targetLeagues,
      metadata: {
        ...metadata,
        processing_time: new Date().toISOString()
      }
    }

    console.log(`✅ Auto-import complete:`, result)

    return NextResponse.json(result)

  } catch (error) {
    console.error('Auto-import failed:', error)
    return NextResponse.json({
      error: 'Auto-import failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check what's available
export async function GET() {
  try {
    // Just fetch data without importing to see what's available
    const scrapedData = await scrapePrizePicksData(['NFL', 'WNBA', 'NCAAF', 'MLB'])
    const { props, metadata } = scrapedData

    // Group by league for summary
    const summary = props.reduce((acc, prop) => {
      if (!acc[prop.sport]) {
        acc[prop.sport] = 0
      }
      acc[prop.sport]++
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      available_props: props.length,
      by_league: summary,
      metadata,
      sample_props: props.slice(0, 5).map(p => ({
        player: p.player_name,
        team: p.team,
        sport: p.sport,
        prop_type: p.prop_type,
        line: p.line_value,
        game_time: p.game_time
      }))
    })

  } catch (error) {
    console.error('Failed to check available props:', error)
    return NextResponse.json({
      error: 'Failed to fetch available props',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
