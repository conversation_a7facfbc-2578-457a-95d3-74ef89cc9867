// Production-Ready Batch Analysis API
// Claude batch research + DeepSeek breakdown pipeline

import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/server'

interface BulkAnalysisResult {
  analyzed: number
  cached: number
  failed: number
  total_cost: number
  claude_cost: number
  deepseek_cost: number
  average_cost_per_prop: number
  total_props: number
  processing_time_ms: number
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { maxAnalyses = 50, forceRefresh = false } = await request.json()

    const supabase = createSupabaseServerClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log(`🚀 Starting batch analysis v2 (max: ${maxAnalyses}, force: ${forceRefresh})`)

    // Get active props for today and tomorrow
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)

    const { data: props, error: propsError } = await supabase
      .from('prizepicks_props')
      .select('*')
      .eq('is_active', true)
      .gte('game_time', new Date().toISOString())
      .lt('game_time', tomorrow.toISOString())
      .order('game_time', { ascending: true })

    if (propsError) {
      throw new Error(`Failed to fetch props: ${propsError.message}`)
    }

    if (!props || props.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No active props found for analysis',
        result: {
          analyzed: 0,
          cached: 0,
          failed: 0,
          total_cost: 0,
          claude_cost: 0,
          deepseek_cost: 0,
          average_cost_per_prop: 0,
          total_props: 0,
          processing_time_ms: Date.now() - startTime
        }
      })
    }

    // Determine which props need analysis
    const propsNeedingAnalysis = []
    
    for (const prop of props) {
      if (forceRefresh) {
        propsNeedingAnalysis.push(prop)
        continue
      }

      // Check for existing analysis (within 4 hours)
      const { data: existingAnalysis } = await supabase
        .from('ai_analyses')
        .select('created_at')
        .eq('prop_id', prop.id)
        .gte('created_at', new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (!existingAnalysis) {
        propsNeedingAnalysis.push(prop)
        continue
      }

      // Check if line has changed since last analysis
      const { data: recentMovement } = await supabase
        .from('line_movements')
        .select('detected_at')
        .eq('prop_id', prop.id)
        .gte('detected_at', existingAnalysis.created_at)
        .order('detected_at', { ascending: false })
        .limit(1)
        .single()

      if (recentMovement) {
        propsNeedingAnalysis.push(prop)
      }
    }

    // Sort by priority and limit
    const sortedProps = propsNeedingAnalysis
      .map(prop => ({
        ...prop,
        priority: calculatePropPriority(prop)
      }))
      .sort((a, b) => b.priority - a.priority)
      .slice(0, maxAnalyses)

    console.log(`📊 Props needing analysis: ${sortedProps.length}/${props.length}`)

    if (sortedProps.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All props have recent analysis',
        result: {
          analyzed: 0,
          cached: props.length,
          failed: 0,
          total_cost: 0,
          claude_cost: 0,
          deepseek_cost: 0,
          average_cost_per_prop: 0,
          total_props: props.length,
          processing_time_ms: Date.now() - startTime
        }
      })
    }

    // Prepare data for batch analysis
    const batchData = sortedProps.map(prop => ({
      id: prop.id,
      player_name: prop.player_name,
      team: prop.team || '',
      opponent: prop.opponent || '',
      prop_type: prop.prop_type,
      line_value: prop.line_value,
      game_time: prop.game_time,
      priority: prop.priority
    }))

    // Run batch analysis
    console.log(`🧠 Running batch Claude + DeepSeek analysis...`)

    // Dynamic import to avoid build issues
    const { batchClaudeAnalysis } = await import('@/lib/analysis/batch-claude-analyzer')
    const analysisResults = await batchClaudeAnalysis(batchData)

    // Save results to database
    let analyzed = 0
    let failed = 0
    let totalCost = 0
    let claudeCost = 0
    let deepSeekCost = 0

    for (const result of analysisResults) {
      try {
        const { error: saveError } = await supabase
          .from('ai_analyses')
          .upsert({
            prop_id: result.prop_id,
            analysis_text: result.analysis_text,
            confidence_rating: result.confidence_rating,
            recommendation: result.recommendation,
            reasoning_points: result.reasoning_points,
            edge_summary: result.edge_summary,
            created_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString() // 6 hours
          })

        if (saveError) {
          console.error(`Failed to save analysis for ${result.prop_id}:`, saveError)
          failed++
        } else {
          analyzed++
          totalCost += result.cost_breakdown.total_cost
          claudeCost += result.cost_breakdown.claude_cost
          deepSeekCost += result.cost_breakdown.deepseek_cost
        }
      } catch (error) {
        console.error(`Failed to process result for ${result.prop_id}:`, error)
        failed++
      }
    }

    const processingTime = Date.now() - startTime
    const result: BulkAnalysisResult = {
      analyzed,
      cached: props.length - propsNeedingAnalysis.length,
      failed,
      total_cost: totalCost,
      claude_cost: claudeCost,
      deepseek_cost: deepSeekCost,
      average_cost_per_prop: analyzed > 0 ? totalCost / analyzed : 0,
      total_props: props.length,
      processing_time_ms: processingTime
    }

    console.log(`✅ Batch analysis v2 complete:`)
    console.log(`   Analyzed: ${analyzed}`)
    console.log(`   Cached: ${result.cached}`)
    console.log(`   Failed: ${failed}`)
    console.log(`   Total cost: $${totalCost.toFixed(3)}`)
    console.log(`   Processing time: ${(processingTime / 1000).toFixed(1)}s`)

    return NextResponse.json({
      success: true,
      message: `Batch analysis completed. Analyzed ${analyzed} props, ${result.cached} served from cache, ${failed} failed.`,
      result
    })

  } catch (error) {
    console.error('Batch analysis v2 error:', error)
    return NextResponse.json(
      { 
        error: 'Batch analysis failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        processing_time_ms: Date.now() - startTime
      }, 
      { status: 500 }
    )
  }
}

// Calculate prop priority for analysis ordering
function calculatePropPriority(prop: any): number {
  let priority = 0

  // High-value prop types
  const highValueProps = ['points', 'strikeouts', 'hits', 'rebounds', 'assists', 'passing_yards']
  if (highValueProps.some(type => prop.prop_type.toLowerCase().includes(type))) {
    priority += 30
  }

  // Prime time games (7-10 PM)
  const gameHour = new Date(prop.game_time).getHours()
  if (gameHour >= 19 && gameHour <= 22) {
    priority += 20
  }

  // Popular sports
  if (['NFL', 'NBA', 'MLB'].includes(prop.sport)) {
    priority += 15
  }

  // Recent props (within 24 hours)
  const hoursUntilGame = (new Date(prop.game_time).getTime() - Date.now()) / (1000 * 60 * 60)
  if (hoursUntilGame <= 24) {
    priority += 25
  }

  return Math.min(priority, 100)
}

// Get analysis status
export async function GET() {
  try {
    const supabase = createSupabaseServerClient()
    
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    // Get total props for today
    const { count: totalProps } = await supabase
      .from('prizepicks_props')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('game_time', today.toISOString())
      .lt('game_time', tomorrow.toISOString())

    // Get analyzed props (within 6 hours)
    const { count: analyzedProps } = await supabase
      .from('ai_analyses')
      .select(`
        prop_id,
        prizepicks_props!inner(game_time)
      `, { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString())
      .gte('prizepicks_props.game_time', today.toISOString())
      .lt('prizepicks_props.game_time', tomorrow.toISOString())

    const completionPercentage = totalProps ? (analyzedProps / totalProps) * 100 : 0

    return NextResponse.json({
      total_props: totalProps || 0,
      analyzed_props: analyzedProps || 0,
      pending_analysis: (totalProps || 0) - (analyzedProps || 0),
      completion_percentage: Math.round(completionPercentage * 10) / 10,
      ready_for_betting: completionPercentage >= 80,
      last_updated: new Date().toISOString()
    })

  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json({ error: 'Status check failed' }, { status: 500 })
  }
}
