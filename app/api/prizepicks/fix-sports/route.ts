// Fix sport classification for existing props
// Updates MLB props that were incorrectly classified as NFL

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🔧 Starting sport classification fix...')

    // Get all props that might be misclassified
    const { data: props, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, prop_type, created_at')
      // Remove the is_active filter to see all props
      // .eq('is_active', true)

    if (fetchError) {
      throw new Error(`Failed to fetch props: ${fetchError.message}`)
    }

    if (!props || props.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No props found to fix',
        fixed: 0
      })
    }

    const updates = []
    
    // MLB prop types that indicate baseball
    const mlbPropTypes = [
      'hits', 'total_bases', 'runs_batted_in', 'runs_scored', 'home_runs',
      'strikeouts', 'walks', 'stolen_bases', 'hits_runs_rbis',
      'pitcher_wins', 'innings_pitched', 'earned_runs_allowed',
      'rbis', 'runs', 'hrs', 'ks', 'bbs', 'sbs', 'h_r_rbi'
    ]

    // NFL prop types
    const nflPropTypes = [
      'passing_yards', 'rushing_yards', 'receiving_yards', 'receptions',
      'passing_touchdowns', 'rushing_touchdowns', 'receiving_touchdowns',
      'rush_rec_touchdowns', 'rush_attempts', 'pass_attempts',
      'tackles_assists', 'sacks', 'interceptions', 'field_goals_made'
    ]

    // NBA/WNBA prop types
    const basketballPropTypes = [
      'points', 'rebounds', 'assists', 'points_rebounds_assists',
      'three_pointers_made', 'points_assists', 'points_rebounds',
      'rebounds_assists', 'steals', 'blocks', 'turnovers'
    ]

    for (const prop of props) {
      let correctSport = prop.sport
      
      // Determine correct sport based on prop type
      if (mlbPropTypes.includes(prop.prop_type)) {
        correctSport = 'MLB'
      } else if (nflPropTypes.includes(prop.prop_type)) {
        correctSport = 'NFL'
      } else if (basketballPropTypes.includes(prop.prop_type)) {
        // Could be NBA or WNBA - check team names or other indicators
        correctSport = 'NBA' // Default to NBA for now
      }

      // Also check for common MLB team abbreviations
      const mlbTeams = [
        'ARI', 'ATL', 'BAL', 'BOS', 'CHC', 'CWS', 'CIN', 'CLE', 'COL',
        'DET', 'HOU', 'KC', 'LAA', 'LAD', 'MIA', 'MIL', 'MIN', 'NYM',
        'NYY', 'OAK', 'PHI', 'PIT', 'SD', 'SF', 'SEA', 'STL', 'TB', 'TEX', 'TOR', 'WSH'
      ]

      if (mlbTeams.includes(prop.team) && mlbPropTypes.includes(prop.prop_type)) {
        correctSport = 'MLB'
      }

      // If sport needs to be updated
      if (correctSport !== prop.sport) {
        updates.push({
          id: prop.id,
          old_sport: prop.sport,
          new_sport: correctSport,
          player_name: prop.player_name,
          team: prop.team,
          prop_type: prop.prop_type
        })
      }
    }

    if (updates.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No sport classifications need fixing',
        fixed: 0,
        total_reviewed: props.length
      })
    }

    console.log(`Found ${updates.length} props that need sport classification fixes:`)
    updates.forEach(update => {
      console.log(`- ${update.player_name} (${update.team}): ${update.old_sport} → ${update.new_sport}`)
    })

    // Update the sports in batches
    let fixed = 0
    for (const update of updates) {
      const { error: updateError } = await supabase
        .from('prizepicks_props')
        .update({ sport: update.new_sport })
        .eq('id', update.id)

      if (updateError) {
        console.error(`Failed to update ${update.id}:`, updateError.message)
      } else {
        fixed++
      }
    }

    console.log(`✅ Successfully fixed ${fixed} sport classifications`)

    return NextResponse.json({
      success: true,
      message: `Successfully fixed ${fixed} sport classifications`,
      fixed,
      total_reviewed: props.length,
      updates: updates.map(u => ({
        player_name: u.player_name,
        team: u.team,
        prop_type: u.prop_type,
        old_sport: u.old_sport,
        new_sport: u.new_sport
      }))
    })

  } catch (error) {
    console.error('Sport fix error:', error)
    return NextResponse.json(
      { 
        error: 'Sport fix failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}

// Get preview of sport classification issues
export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()

    const { data: props, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, prop_type, created_at')
      // .eq('is_active', true)  // Remove filter to see all props
      .order('created_at', { ascending: false })
      .limit(100)

    if (fetchError) {
      throw new Error(`Failed to fetch props: ${fetchError.message}`)
    }

    // Analyze sport classifications
    const analysis = {
      total: props?.length || 0,
      by_sport: {} as Record<string, number>,
      potential_issues: [] as any[]
    }

    const mlbPropTypes = ['hits', 'total_bases', 'runs_batted_in', 'runs_scored', 'home_runs', 'strikeouts', 'rbis']
    const mlbTeams = ['ARI', 'ATL', 'BAL', 'BOS', 'CHC', 'CWS', 'CIN', 'CLE', 'COL', 'DET', 'HOU', 'KC']

    props?.forEach(prop => {
      // Count by sport
      analysis.by_sport[prop.sport] = (analysis.by_sport[prop.sport] || 0) + 1

      // Check for potential misclassifications
      if (prop.sport === 'NFL' && mlbPropTypes.includes(prop.prop_type)) {
        analysis.potential_issues.push({
          ...prop,
          issue: 'NFL sport with MLB prop type'
        })
      }

      if (prop.sport === 'NFL' && mlbTeams.includes(prop.team)) {
        analysis.potential_issues.push({
          ...prop,
          issue: 'NFL sport with MLB team'
        })
      }
    })

    return NextResponse.json({
      analysis,
      potential_fixes: analysis.potential_issues.length
    })

  } catch (error) {
    console.error('Analysis error:', error)
    return NextResponse.json({ error: 'Analysis failed' }, { status: 500 })
  }
}
