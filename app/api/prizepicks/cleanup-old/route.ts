import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has authorized email
    const authorizedEmails = ['<EMAIL>', '<EMAIL>']
    if (!authorizedEmails.includes(user.email || '')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    console.log('🧹 Starting cleanup of old props...')

    // Get current time
    const now = new Date()
    const cutoffTime = new Date(now.getTime() - (24 * 60 * 60 * 1000)) // 24 hours ago

    // First, let's see what we have
    const { data: allProps, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, game_time, created_at, is_active')
      .order('game_time', { ascending: true })

    if (fetchError) {
      throw new Error(`Failed to fetch props: ${fetchError.message}`)
    }

    console.log(`📊 Found ${allProps?.length || 0} total props`)

    // Identify old props (games that have already happened)
    const oldProps = allProps?.filter(prop => {
      const gameTime = new Date(prop.game_time)
      return gameTime < now
    }) || []

    console.log(`🗑️ Found ${oldProps.length} old props (games already happened)`)

    if (oldProps.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No old props to clean up',
        cleaned: 0,
        total_reviewed: allProps?.length || 0
      })
    }

    // Delete old props
    const oldPropIds = oldProps.map(p => p.id)
    const { error: deleteError } = await supabase
      .from('prizepicks_props')
      .delete()
      .in('id', oldPropIds)

    if (deleteError) {
      throw new Error(`Failed to delete old props: ${deleteError.message}`)
    }

    // Also clean up any AI analyses for deleted props
    const { error: analysisDeleteError } = await supabase
      .from('ai_analyses')
      .delete()
      .in('prop_id', oldPropIds)

    if (analysisDeleteError) {
      console.warn('Failed to clean up AI analyses:', analysisDeleteError)
    }

    const result = {
      success: true,
      message: `Cleaned up ${oldProps.length} old props`,
      cleaned: oldProps.length,
      total_reviewed: allProps?.length || 0,
      deleted_props: oldProps.slice(0, 10).map(p => ({
        player_name: p.player_name,
        team: p.team,
        sport: p.sport,
        game_time: p.game_time
      }))
    }

    console.log('✅ Cleanup complete:', result)

    return NextResponse.json(result)

  } catch (error) {
    console.error('Cleanup failed:', error)
    return NextResponse.json({
      error: 'Cleanup failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to preview what would be cleaned
export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()

    const now = new Date()
    
    // Get all props
    const { data: allProps, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, game_time, created_at, is_active')
      .order('game_time', { ascending: true })

    if (fetchError) {
      throw new Error(`Failed to fetch props: ${fetchError.message}`)
    }

    // Identify old props
    const oldProps = allProps?.filter(prop => {
      const gameTime = new Date(prop.game_time)
      return gameTime < now
    }) || []

    const activeProps = allProps?.filter(prop => {
      const gameTime = new Date(prop.game_time)
      return gameTime >= now
    }) || []

    // Group by sport
    const oldBySport = oldProps.reduce((acc, prop) => {
      if (!acc[prop.sport]) acc[prop.sport] = 0
      acc[prop.sport]++
      return acc
    }, {} as Record<string, number>)

    const activeBySport = activeProps.reduce((acc, prop) => {
      if (!acc[prop.sport]) acc[prop.sport] = 0
      acc[prop.sport]++
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      total_props: allProps?.length || 0,
      old_props: oldProps.length,
      active_props: activeProps.length,
      old_by_sport: oldBySport,
      active_by_sport: activeBySport,
      sample_old_props: oldProps.slice(0, 5).map(p => ({
        player: p.player_name,
        team: p.team,
        sport: p.sport,
        game_time: p.game_time
      })),
      sample_active_props: activeProps.slice(0, 5).map(p => ({
        player: p.player_name,
        team: p.team,
        sport: p.sport,
        game_time: p.game_time
      }))
    })

  } catch (error) {
    console.error('Failed to preview cleanup:', error)
    return NextResponse.json({
      error: 'Failed to preview cleanup',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
