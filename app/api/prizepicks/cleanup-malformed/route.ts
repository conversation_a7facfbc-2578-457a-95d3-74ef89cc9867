// Cleanup API for malformed PrizePicks entries
// Removes entries with invalid player names like "DET - OF"

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🧹 Starting cleanup of malformed PrizePicks entries...')

    // Find malformed entries
    const { data: malformedEntries, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport')
      .or(
        // Player names that look like team-position codes
        'player_name.like.%-%,' +
        // Player names that are too short (less than 3 characters)
        'player_name.like.__,' +
        // Player names that are all caps and short (likely team codes)
        'player_name.like.___'
      )

    if (fetchError) {
      throw new Error(`Failed to fetch malformed entries: ${fetchError.message}`)
    }

    if (!malformedEntries || malformedEntries.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No malformed entries found',
        cleaned: 0
      })
    }

    // Filter for truly malformed entries
    const toDelete = malformedEntries.filter(entry => {
      const name = entry.player_name || ''
      
      // Check for team-position patterns like "DET - OF"
      if (name.match(/^[A-Z]{2,4}\s*-\s*[A-Z]{1,3}$/)) {
        return true
      }
      
      // Check for entries that are just team codes
      if (name.length <= 3 && name.match(/^[A-Z]+$/)) {
        return true
      }
      
      // Check for entries with no real name content
      if (name.length < 3) {
        return true
      }
      
      return false
    })

    if (toDelete.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No truly malformed entries found after filtering',
        cleaned: 0,
        candidates_reviewed: malformedEntries.length
      })
    }

    console.log(`Found ${toDelete.length} malformed entries to delete:`)
    toDelete.forEach(entry => {
      console.log(`- ID: ${entry.id}, Name: "${entry.player_name}", Team: ${entry.team}, Sport: ${entry.sport}`)
    })

    // Delete malformed entries
    const idsToDelete = toDelete.map(entry => entry.id)
    const { error: deleteError } = await supabase
      .from('prizepicks_props')
      .delete()
      .in('id', idsToDelete)

    if (deleteError) {
      throw new Error(`Failed to delete malformed entries: ${deleteError.message}`)
    }

    console.log(`✅ Successfully cleaned up ${toDelete.length} malformed entries`)

    return NextResponse.json({
      success: true,
      message: `Successfully cleaned up ${toDelete.length} malformed entries`,
      cleaned: toDelete.length,
      candidates_reviewed: malformedEntries.length,
      deleted_entries: toDelete.map(entry => ({
        id: entry.id,
        player_name: entry.player_name,
        team: entry.team,
        sport: entry.sport
      }))
    })

  } catch (error) {
    console.error('Cleanup error:', error)
    return NextResponse.json(
      { 
        error: 'Cleanup failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}

// Get preview of malformed entries without deleting
export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()

    // Find potentially malformed entries
    const { data: entries, error: fetchError } = await supabase
      .from('prizepicks_props')
      .select('id, player_name, team, sport, created_at')
      .or(
        'player_name.like.%-%,' +
        'player_name.like.__,' +
        'player_name.like.___'
      )
      .order('created_at', { ascending: false })
      .limit(50)

    if (fetchError) {
      throw new Error(`Failed to fetch entries: ${fetchError.message}`)
    }

    // Categorize entries
    const malformed = []
    const suspicious = []
    const valid = []

    entries?.forEach(entry => {
      const name = entry.player_name || ''
      
      if (name.match(/^[A-Z]{2,4}\s*-\s*[A-Z]{1,3}$/)) {
        malformed.push({ ...entry, reason: 'Team-position pattern' })
      } else if (name.length <= 3 && name.match(/^[A-Z]+$/)) {
        malformed.push({ ...entry, reason: 'Team code only' })
      } else if (name.length < 3) {
        malformed.push({ ...entry, reason: 'Too short' })
      } else if (name.split(' ').length < 2) {
        suspicious.push({ ...entry, reason: 'Single word name' })
      } else {
        valid.push({ ...entry, reason: 'Appears valid' })
      }
    })

    return NextResponse.json({
      total_reviewed: entries?.length || 0,
      malformed: malformed.length,
      suspicious: suspicious.length,
      valid: valid.length,
      entries: {
        malformed,
        suspicious: suspicious.slice(0, 10), // Limit for response size
        valid: valid.slice(0, 5) // Just a few examples
      }
    })

  } catch (error) {
    console.error('Preview error:', error)
    return NextResponse.json({ error: 'Preview failed' }, { status: 500 })
  }
}
